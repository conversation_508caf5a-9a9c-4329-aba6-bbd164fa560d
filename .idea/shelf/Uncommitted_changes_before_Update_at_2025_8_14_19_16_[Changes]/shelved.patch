Index: src/components/MCP/BaseMCPCard/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Flex, Divider, Typography, Tooltip} from 'antd';\nimport {memo, MouseEvent, useMemo, ReactNode} from 'react';\nimport {Button} from '@panda-design/components';\nimport cx from 'classnames';\nimport {MCPServerBase} from '@/types/mcp/mcp';\nimport {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';\nimport MCPServerAvatar from '@/components/MCP/MCPServerAvatar';\nimport MCPCard from '@/design/MCP/MCPCard';\nimport {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';\nimport TagGroup from '@/components/MCP/TagGroup';\nimport {MCPCollectButton} from '@/components/MCP/MCPCollectButton';\nimport PublishInfo from '@/components/MCP/PublishInfo';\nimport UpdateInfo from '@/components/MCP/UpdateInfo';\nimport SvgEye from '@/icons/mcp/Eye';\nimport {\n    actionButtonHoverStyle,\n    cardContentStyle,\n    containerCss,\n    DescriptionContainer,\n    DescriptionText,\n    departmentTextStyle,\n    dividerStyle,\n    EllipsisOverlay,\n    formatCount,\n    hoverActionsStyle,\n    iconStyle,\n    protocolTextStyle,\n    statsContainerStyle,\n} from './BaseMCPCard.styles';\n\ninterface Props {\n    server: MCPServerBase;\n    refresh: () => void;\n    showDepartment?: boolean;\n    workspaceId?: number;\n    onCardClick: () => void;\n    onViewCountClick: (e: MouseEvent) => void;\n    onPlaygroundClick?: (e: MouseEvent) => void;\n    renderActions?: () => ReactNode;\n    infoType?: 'publish' | 'update';\n}\n\nconst BaseMCPCard = ({\n    server,\n    refresh,\n    showDepartment = false,\n    workspaceId,\n    onCardClick,\n    onViewCountClick,\n    onPlaygroundClick,\n    renderActions,\n    infoType = 'publish',\n}: Props) => {\n    const tags = useMemo(\n        () => (server.labels ?? []).map((label, index) => ({\n            id: label.id || index,\n            label: label.labelValue,\n        })),\n        [server.labels]\n    );\n\n    return (\n        <MCPCard vertical onClick={onCardClick} className={containerCss}>\n            <Flex gap={14} align=\"center\">\n                <MCPServerAvatar icon={server.icon} />\n                <Flex vertical justify=\"space-between\" style={cardContentStyle} gap={4}>\n                    <Typography.Title level={4} ellipsis>\n                        {server.name}\n                    </Typography.Title>\n                    <Flex align=\"center\" gap={12}>\n                        <Typography.Text style={protocolTextStyle}>\n                            {getServerTypeText(server.serverSourceType)} | {server.serverProtocolType}\n                        </Typography.Text>\n                    </Flex>\n                </Flex>\n            </Flex>\n            <Tooltip title={server.description || '暂无描述'} placement=\"top\">\n                <DescriptionContainer>\n                    <DescriptionText>{server.description || '暂无描述'}</DescriptionText>\n                    <EllipsisOverlay />\n                </DescriptionContainer>\n            </Tooltip>\n            {showDepartment && (\n                <Typography.Text style={departmentTextStyle}>{server.departmentName || '暂无部门信息'}</Typography.Text>\n            )}\n            <TagGroup\n                labels={tags}\n                color=\"light-purple\"\n                prefix={null}\n                style={{flexShrink: 1, overflow: 'hidden'}}\n                gap={4}\n            />\n            <Divider style={dividerStyle} />\n            <Flex justify=\"space-between\" align=\"center\">\n                <Flex align=\"center\" gap={12}>\n                    <Tooltip title=\"浏览量\">\n                        <Flex\n                            align=\"center\"\n                            gap={4}\n                            onClick={onViewCountClick}\n                            className={statsContainerStyle}\n                        >\n                            <SvgEye style={iconStyle} />\n                            {formatCount(server.viewCount)}\n                        </Flex>\n                    </Tooltip>\n                </Flex>\n                {infoType === 'update' ? (\n                    <UpdateInfo username={server.lastModifyUser} time={server.lastModifyTime} variant=\"space-card\" />\n                ) : (\n                    <PublishInfo username={server.publishUser} time={server.publishTime} />\n                )}\n            </Flex>\n            <Flex align=\"center\" justify=\"space-between\" gap={10} className={`hover-actions ${hoverActionsStyle}`}>\n                {renderActions ? renderActions() : (\n                    <>\n                        <MCPCollectButton\n                            refresh={refresh}\n                            favorite={server.favorite}\n                            serverId={server.id}\n                            className={cx(actionButtonHoverStyle)}\n                            showText={false}\n                            iconColor=\"#0083FF\"\n                        />\n                        <MCPSubscribeButton\n                            refresh={refresh}\n                            workspaceId={workspaceId || server.workspaceId}\n                            id={server.id}\n                            className={cx(actionButtonHoverStyle)}\n                            showText={false}\n                            iconColor=\"#0083FF\"\n                        />\n                        <Button type=\"primary\" onClick={onPlaygroundClick}>\n                            去MCP Playground使用\n                        </Button>\n                    </>\n                )}\n            </Flex>\n        </MCPCard>\n    );\n};\n\nexport default memo(BaseMCPCard);\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/BaseMCPCard/index.tsx b/src/components/MCP/BaseMCPCard/index.tsx
--- a/src/components/MCP/BaseMCPCard/index.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/components/MCP/BaseMCPCard/index.tsx	(date 1755170013200)
@@ -1,7 +1,7 @@
+/* eslint-disable max-lines */
 import {Flex, Divider, Typography, Tooltip} from 'antd';
 import {memo, MouseEvent, useMemo, ReactNode} from 'react';
 import {Button} from '@panda-design/components';
-import cx from 'classnames';
 import {MCPServerBase} from '@/types/mcp/mcp';
 import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
 import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
@@ -9,19 +9,19 @@
 import {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
 import TagGroup from '@/components/MCP/TagGroup';
 import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
-import PublishInfo from '@/components/MCP/PublishInfo';
-import UpdateInfo from '@/components/MCP/UpdateInfo';
 import SvgEye from '@/icons/mcp/Eye';
+import SvgCallCount from '@/icons/mcp/CallCount';
+import SvgPremium from '@/icons/mcp/Vip';
 import {
-    actionButtonHoverStyle,
+    actionButtonStyle,
     cardContentStyle,
     containerCss,
     DescriptionContainer,
     DescriptionText,
-    departmentTextStyle,
     dividerStyle,
     EllipsisOverlay,
     formatCount,
+    fullWidthButtonStyle,
     hoverActionsStyle,
     iconStyle,
     protocolTextStyle,
@@ -43,13 +43,11 @@
 const BaseMCPCard = ({
     server,
     refresh,
-    showDepartment = false,
     workspaceId,
     onCardClick,
     onViewCountClick,
     onPlaygroundClick,
     renderActions,
-    infoType = 'publish',
 }: Props) => {
     const tags = useMemo(
         () => (server.labels ?? []).map((label, index) => ({
@@ -62,15 +60,38 @@
     return (
         <MCPCard vertical onClick={onCardClick} className={containerCss}>
             <Flex gap={14} align="center">
-                <MCPServerAvatar icon={server.icon} />
+                <div style={{position: 'relative', display: 'inline-block'}}>
+                    <MCPServerAvatar
+                        icon={server.icon}
+                        style={{
+                            border: '2px solid',
+                            borderImageSource:
+                                'linear-gradient(237.19deg, #0183FF -52.14%, rgba(173, 215, 255, 0.6) 111.4%)',
+                            borderImageSlice: 1,
+                        }}
+                    />
+                    <SvgPremium
+                        style={{
+                            position: 'absolute',
+                            bottom: -7,
+                            right: -4,
+                            fontSize: '23px',
+                        }}
+                    />
+                </div>
                 <Flex vertical justify="space-between" style={cardContentStyle} gap={4}>
                     <Typography.Title level={4} ellipsis>
                         {server.name}
                     </Typography.Title>
-                    <Flex align="center" gap={12}>
+                    <Flex align="center" gap={4}>
+                        <Typography.Text style={protocolTextStyle}>
+                            {getServerTypeText(server.serverProtocolType)}
+                        </Typography.Text>
+                        <Divider type="vertical" style={{borderColor: '#D9D9D9'}} />
                         <Typography.Text style={protocolTextStyle}>
-                            {getServerTypeText(server.serverSourceType)} | {server.serverProtocolType}
+                            {server.serverProtocolType}
                         </Typography.Text>
+
                     </Flex>
                 </Flex>
             </Flex>
@@ -80,9 +101,6 @@
                     <EllipsisOverlay />
                 </DescriptionContainer>
             </Tooltip>
-            {showDepartment && (
-                <Typography.Text style={departmentTextStyle}>{server.departmentName || '暂无部门信息'}</Typography.Text>
-            )}
             <TagGroup
                 labels={tags}
                 color="light-purple"
@@ -101,39 +119,43 @@
                             className={statsContainerStyle}
                         >
                             <SvgEye style={iconStyle} />
-                            {formatCount(server.viewCount)}
+                            {formatCount(server.serverMetrics?.viewCount || 0)}
                         </Flex>
                     </Tooltip>
-                </Flex>
-                {infoType === 'update' ? (
-                    <UpdateInfo username={server.lastModifyUser} time={server.lastModifyTime} variant="space-card" />
-                ) : (
-                    <PublishInfo username={server.publishUser} time={server.publishTime} />
-                )}
-            </Flex>
-            <Flex align="center" justify="space-between" gap={10} className={`hover-actions ${hoverActionsStyle}`}>
-                {renderActions ? renderActions() : (
-                    <>
-                        <MCPCollectButton
-                            refresh={refresh}
-                            favorite={server.favorite}
-                            serverId={server.id}
-                            className={cx(actionButtonHoverStyle)}
-                            showText={false}
-                            iconColor="#0083FF"
-                        />
-                        <MCPSubscribeButton
-                            refresh={refresh}
-                            workspaceId={workspaceId || server.workspaceId}
-                            id={server.id}
-                            className={cx(actionButtonHoverStyle)}
-                            showText={false}
-                            iconColor="#0083FF"
-                        />
-                        <Button type="primary" onClick={onPlaygroundClick}>
-                            去MCP Playground使用
-                        </Button>
-                    </>
+                    <Tooltip title="调用量">
+                        <Flex
+                            align="center"
+                            gap={4}
+                            className={statsContainerStyle}
+                        >
+                            <SvgCallCount style={iconStyle} />
+                            {formatCount(server.serverMetrics?.callCount || 0)}
+                        </Flex>
+                    </Tooltip>
+                </Flex>
+                <Flex align="center">
+                    <MCPCollectButton
+                        refresh={refresh}
+                        favorite={server.favorite}
+                        serverId={server.id}
+                        showText
+                        style={actionButtonStyle}
+                    />
+                    <Divider type="vertical" style={{borderColor: '#D9D9D9'}} />
+                    <MCPSubscribeButton
+                        refresh={refresh}
+                        workspaceId={workspaceId || server.workspaceId}
+                        id={server.id}
+                        showText
+                        style={actionButtonStyle}
+                    />
+                </Flex>
+            </Flex>
+            <Flex align="center" justify="space-between" gap={10} className={`hover-actions ${hoverActionsStyle}`}>
+                {renderActions ? renderActions() : (
+                    <Button type="primary" onClick={onPlaygroundClick} style={fullWidthButtonStyle}>
+                        去MCP Playground使用
+                    </Button>
                 )}
             </Flex>
         </MCPCard>
Index: src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {useCallback, useEffect, useMemo, useState} from 'react';\nimport {Flex, List, Space} from 'antd';\nimport InfiniteScroll from 'react-infinite-scroll-component';\nimport styled from '@emotion/styled';\nimport {apiGetMCPServerListByWorkspace} from '@/api/mcp';\nimport {useMCPWorkspaceId} from '@/components/MCP/hooks';\nimport {MCPServerBase, MCPServerStatus} from '@/types/mcp/mcp';\nimport {RadioButtonGroup} from '@/components/MCP/RadioButtonGroup';\nimport {Gap} from '@/design/iplayground/Gap';\nimport CreateMCPAppModal from '@/components/MCP/CreateMCPAppButton/CreateMCPAppModal';\nimport MCPEmpty from '@/design/MCP/MCPEmpty';\nimport SpaceMCPCard from './SpaceMCPCard';\nimport {useLoadMore} from './hooks';\n\nconst PAGE_SIZE = 18;\n\nconst Container = styled.div`\n    flex: 1;\n    overflow-y: auto;\n    ::-webkit-scrollbar {\n        display: none;\n    }\n`;\n\nconst StyledLabel = styled.span`\n    display: inline-flex;\n    gap: 16px;\n    align-items: center;\n    color: #8F8F8F;\n    &: after{\n        content: '';\n        width: 1px;\n        height: 16px;\n        background: #D9D9D9;\n    }\n`;\n\nconst MCPListPanel = () => {\n    const workspaceId = useMCPWorkspaceId();\n    const [selectedStatus, setSelectedStatus] = useState<MCPServerStatus | 'all'>('all');\n\n    const api = useCallback(\n        (params: {current: number, limit: number}) => {\n            return apiGetMCPServerListByWorkspace({\n                workspaceId,\n                size: params.limit,\n                pn: params.current,\n                status: selectedStatus !== 'all' ? selectedStatus : undefined,\n            });\n        },\n        [workspaceId, selectedStatus]\n    );\n\n    const {loadMore, total, list, refresh} = useLoadMore<MCPServerBase>(api, PAGE_SIZE);\n\n    // TODO：接口上线后移除\n    const dataSource = useMemo(\n        () => {\n            if (selectedStatus === 'all') {\n                return list;\n            }\n            return list?.filter(item => item.serverStatus === selectedStatus);\n        },\n        [list, selectedStatus]\n    );\n    // const [newFirst, setNewFirst] = useState(true);\n    useEffect(\n        () => {\n            refresh();\n        },\n        [refresh]\n    );\n\n    return (\n        <Container id=\"scrollableDiv\">\n            <Space direction=\"vertical\">\n                <Space>\n                    <StyledLabel>MCP状态</StyledLabel>\n                    <RadioButtonGroup\n                        value={selectedStatus}\n                        onChange={setSelectedStatus}\n                        options={[\n                            {label: '全部', value: 'all'},\n                            {label: '已发布', value: 'release'},\n                            {label: '草稿', value: 'draft'},\n                        ]}\n                    />\n                </Space>\n                {/* <Space>\n                    <span>发布时间</span>\n                    <Button\n                        onClick={() => setNewFirst(value => !value)}\n                        type=\"text\"\n                    >\n                        {newFirst ? '新到旧' : '旧到新'} <StyledIcon newFirst={newFirst} />\n                    </Button>\n                </Space> */}\n            </Space>\n            <Gap />\n            <InfiniteScroll\n                // 挂一个key，避免切换type的时候出问题\n                key={selectedStatus}\n                style={{overflow: 'none'}}\n                dataLength={list.length || 0}\n                next={loadMore}\n                hasMore={total > list.length}\n                loader={<Flex justify=\"center\" align=\"center\"><div>加载中...</div></Flex>}\n                // endMessage={<Divider plain>到底了</Divider>}\n                scrollableTarget=\"scrollableDiv\"\n            >\n                {(\n                    <List\n                        dataSource={dataSource}\n                        grid={{\n                            gutter: 12,\n                            column: 2,\n                            xs: 2,\n                            sm: 3,\n                            md: 3,\n                            lg: 3,\n                            xl: 3,\n                            xxl: 3,\n                        }}\n                        rowKey=\"id\"\n                        renderItem={item => (\n                            <List.Item key={item.id}>\n                                <SpaceMCPCard refresh={refresh} server={item} />\n                            </List.Item>\n                        )}\n                        locale={{\n                            emptyText: (\n                                <MCPEmpty\n                                    description={(\n                                        <Flex align=\"center\" justify=\"center\">\n                                            <span>暂无MCP Server</span>\n                                        </Flex>\n                                    )}\n                                />\n                            ),\n                        }}\n                    />\n                )}\n            </InfiniteScroll>\n            <CreateMCPAppModal onSuccess={refresh} />\n        </Container>\n    );\n};\n\nexport default MCPListPanel;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx b/src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx
--- a/src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx	(date 1755158826217)
@@ -35,6 +35,16 @@
     }
 `;
 
+const StyledList = styled(List<MCPServerBase>)`
+    .ant-list-grid .ant-col {
+        @media (min-width: 1600px) {
+            width: 25% !important;
+            max-width: 25% !important;
+            flex: 0 0 25% !important;
+        }
+    }
+`;
+
 const MCPListPanel = () => {
     const workspaceId = useMCPWorkspaceId();
     const [selectedStatus, setSelectedStatus] = useState<MCPServerStatus | 'all'>('all');
@@ -109,10 +119,10 @@
                 scrollableTarget="scrollableDiv"
             >
                 {(
-                    <List
+                    <StyledList
                         dataSource={dataSource}
                         grid={{
-                            gutter: 12,
+                            gutter: 20,
                             column: 2,
                             xs: 2,
                             sm: 3,
Index: src/icons/mcp/Code.tsx
===================================================================
diff --git a/src/icons/mcp/Code.tsx b/src/icons/mcp/Code.tsx
deleted file mode 100644
--- a/src/icons/mcp/Code.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ /dev/null	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
@@ -1,17 +0,0 @@
-import type { SVGProps } from "react";
-const SvgCode = (props: SVGProps<SVGSVGElement>) => (
-    <svg
-        xmlns="http://www.w3.org/2000/svg"
-        width="1em"
-        height="1em"
-        fill="none"
-        viewBox="0 0 20 20"
-        {...props}
-    >
-        <path
-            fill="#000"
-            d="M18.334 1.666a.833.833 0 0 1 .833.833v15a.834.834 0 0 1-.834.834H1.668a.833.833 0 0 1-.834-.834v-15a.833.833 0 0 1 .834-.833zM17.5 3.333h-15v13.333h15zM10.277 5.42l.74.083a.416.416 0 0 1 .367.47l-1.071 7.83a.417.417 0 0 1-.459.358l-.74-.082a.416.416 0 0 1-.366-.47l1.07-7.831a.417.417 0 0 1 .46-.358M7.121 7.483l.522.485a.417.417 0 0 1 .015.596l-1.438 1.47 1.438 1.47a.417.417 0 0 1-.007.59l-.008.007-.522.485a.416.416 0 0 1-.58-.012L4.31 10.328a.417.417 0 0 1 0-.587L6.54 7.494a.417.417 0 0 1 .58-.011m6.338.011 2.232 2.247a.417.417 0 0 1 0 .587l-2.232 2.246a.416.416 0 0 1-.58.012l-.522-.485a.42.42 0 0 1-.014-.597l1.437-1.47-1.437-1.47a.417.417 0 0 1 .014-.596l.523-.485a.417.417 0 0 1 .579.011"
-        />
-    </svg>
-);
-export default SvgCode;
Index: src/icons/mcp/code.svg
===================================================================
diff --git a/src/icons/mcp/code.svg b/src/icons/mcp/code.svg
deleted file mode 100644
--- a/src/icons/mcp/code.svg	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ /dev/null	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
@@ -1,3 +0,0 @@
-<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
-<path d="M18.3335 1.66602C18.5545 1.66602 18.7665 1.75381 18.9228 1.91009C19.079 2.06637 19.1668 2.27834 19.1668 2.49935V17.4993C19.1668 17.7204 19.079 17.9323 18.9228 18.0886C18.7665 18.2449 18.5545 18.3327 18.3335 18.3327H1.66683C1.44582 18.3327 1.23385 18.2449 1.07757 18.0886C0.921293 17.9323 0.833496 17.7204 0.833496 17.4993V2.49935C0.833496 2.27834 0.921293 2.06637 1.07757 1.91009C1.23385 1.75381 1.44582 1.66602 1.66683 1.66602H18.3335ZM17.5002 3.33268H2.50016V16.666H17.5002V3.33268ZM10.2772 5.42018L11.0168 5.50268C11.0721 5.50884 11.1255 5.52599 11.174 5.55311C11.2225 5.58024 11.2651 5.6168 11.2993 5.66063C11.3334 5.70447 11.3585 5.75469 11.373 5.80835C11.3874 5.86202 11.391 5.91803 11.3835 5.9731L10.3127 13.8035C10.2979 13.9111 10.2417 14.0086 10.1561 14.0754C10.0705 14.1422 9.96224 14.1729 9.85433 14.161L9.11475 14.0789C9.05951 14.0728 9.00607 14.0556 8.95756 14.0285C8.90905 14.0014 8.86646 13.9648 8.83229 13.921C8.79813 13.8771 8.77307 13.8269 8.75861 13.7733C8.74415 13.7196 8.74057 13.6636 8.74808 13.6085L9.81891 5.77768C9.83368 5.67013 9.88984 5.57258 9.97544 5.50581C10.061 5.43904 10.1693 5.40832 10.2772 5.42018ZM7.12058 7.48268L7.64308 7.96768C7.68386 8.00552 7.71664 8.05114 7.73951 8.10184C7.76238 8.15255 7.77486 8.20733 7.77622 8.26294C7.77758 8.31855 7.76779 8.37387 7.74743 8.42563C7.72706 8.4774 7.69654 8.52457 7.65766 8.56435L6.22016 10.0343L7.65766 11.5043C7.7349 11.5834 7.77759 11.6898 7.77634 11.8003C7.77508 11.9108 7.73 12.0163 7.651 12.0935L7.64308 12.101L7.12058 12.586C7.04138 12.6596 6.9367 12.6995 6.82864 12.6973C6.72057 12.6951 6.61758 12.651 6.54141 12.5744L4.30975 10.3281C4.23214 10.25 4.18858 10.1444 4.18858 10.0343C4.18858 9.92427 4.23214 9.81867 4.30975 9.7406L6.54141 7.49435C6.61758 7.41766 6.72057 7.37358 6.82864 7.3714C6.9367 7.36923 7.04138 7.40913 7.12058 7.48268ZM13.4589 7.49435L15.6906 9.7406C15.7682 9.81867 15.8117 9.92427 15.8117 10.0343C15.8117 10.1444 15.7682 10.25 15.6906 10.3281L13.4589 12.5739C13.3829 12.6507 13.2799 12.695 13.1719 12.6973C13.0638 12.6996 12.9591 12.6599 12.8797 12.5864L12.3572 12.101C12.3165 12.0632 12.2837 12.0176 12.2608 11.9669C12.2379 11.9161 12.2255 11.8614 12.2241 11.8058C12.2227 11.7501 12.2325 11.6948 12.2529 11.6431C12.2733 11.5913 12.3038 11.5441 12.3427 11.5043L13.7802 10.0343L12.3427 8.56435C12.3038 8.52457 12.2733 8.4774 12.2529 8.42563C12.2325 8.37387 12.2227 8.31855 12.2241 8.26294C12.2255 8.20733 12.2379 8.15255 12.2608 8.10184C12.2837 8.05114 12.3165 8.00552 12.3572 7.96768L12.8797 7.48268C12.9589 7.40913 13.0636 7.36923 13.1717 7.3714C13.2798 7.37358 13.3827 7.41766 13.4589 7.49435Z" fill="black"/>
-</svg>
Index: src/icons/mcp/SubscribeBlue.tsx
===================================================================
diff --git a/src/icons/mcp/SubscribeBlue.tsx b/src/icons/mcp/SubscribeBlue.tsx
deleted file mode 100644
--- a/src/icons/mcp/SubscribeBlue.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ /dev/null	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
@@ -1,17 +0,0 @@
-import type { SVGProps } from "react";
-const SvgSubscribe = (props: SVGProps<SVGSVGElement>) => (
-    <svg
-        xmlns="http://www.w3.org/2000/svg"
-        width="1em"
-        height="1em"
-        fill="none"
-        viewBox="0 0 17 16"
-        {...props}
-    >
-        <path
-            fill="#0083FF"
-            d="m4.094 12.914 3.184-1.592.106-.049a1.99 1.99 0 0 1 1.566 0l.106.049 3.184 1.592V2.569H4.093zm9.406.863-.002.05a.728.728 0 0 1-1.005.621l-.045-.02-3.956-1.978a.73.73 0 0 0-.65 0l-3.956 1.977-.046.021a.728.728 0 0 1-1.005-.621l-.002-.05V2.036c0-.39.306-.707.69-.726l.038-.001h9.249c.385.02.69.338.69.727z"
-        />
-    </svg>
-);
-export default SvgSubscribe;
Index: .gitignore
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># See https://help.github.com/articles/ignoring-files/ for more about ignoring files.\n\n# dependencies\n/node_modules\n/.pnp\n.pnp.js\n\n# testing\n/coverage\n\n# production\n/build\n/output\n/.comate-f2c\n\n# misc\n.DS_Store\n.env.local\n.env.development.local\n.env.test.local\n.env.production.local\n\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.gitignore b/.gitignore
--- a/.gitignore	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/.gitignore	(date 1755156128248)
@@ -23,3 +23,34 @@
 npm-debug.log*
 yarn-debug.log*
 yarn-error.log*
+.idea/.gitignore
+.idea/comate-stack-fe.iml
+.idea/modules.xml
+.idea/vcs.xml
+.idea/codeStyles/codeStyleConfig.xml
+.idea/codeStyles/Project.xml
+.idea/inspectionProfiles/Project_Default.xml
+/.augment/rules/mode.md
+/.idea/jsLinters/eslint.xml
+.idea/.gitignore
+.idea/comate-stack-fe.iml
+.idea/modules.xml
+.idea/vcs.xml
+.idea/codeStyles/codeStyleConfig.xml
+.idea/codeStyles/Project.xml
+.idea/inspectionProfiles/Project_Default.xml
+/.augment/rules/mode.md
+/.idea/jsLinters/eslint.xml
+.idea/workspace.xml
+.idea/shelf/Changes.xml
+.idea/shelf/Changes1.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10__Changes_.xml
+.idea/shelf/Changes/shelved.patch
+.idea/shelf/Changes1/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_13_22_18__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_13_22_18_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21__Changes_1.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21_\[Changes]1/shelved.patch
Index: src/components/MCP/MCPServerTypeTag.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Tag} from '@panda-design/components';\nimport {MCPServerType} from '@/types/mcp/mcp';\nimport {IconLocal, IconRemote, IconStandard} from '@/icons/mcp';\n\nexport const SERVER_TYPE_DICT: Record<MCPServerType, string> = {\n    script: 'Local',\n    openapi: 'Remote',\n    external: '标准MCP',\n};\n\nexport const getServerTypeText = (type: MCPServerType) => {\n    return SERVER_TYPE_DICT[type] || SERVER_TYPE_DICT.external;\n};\n\nexport const getServerTypeIcon = (type: MCPServerType) => {\n    switch (type) {\n        case 'script':\n            return <IconLocal />;\n        case 'openapi':\n            return <IconRemote />;\n        case 'external':\n            return <IconStandard />;\n        default:\n            return <IconStandard />;\n    }\n};\n\ninterface Props {\n    type: MCPServerType;\n}\nexport default function MCPServerTypeTag({type}: Props) {\n    return (\n        <Tag\n            type=\"flat\"\n            style={{\n                color: '#3779EA',\n                backgroundColor: '#DFE9FC',\n                margin: 0,\n            }}\n            icon={getServerTypeIcon(type)}\n        >\n            {SERVER_TYPE_DICT[type]}\n        </Tag>\n    );\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/MCPServerTypeTag.tsx b/src/components/MCP/MCPServerTypeTag.tsx
--- a/src/components/MCP/MCPServerTypeTag.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/components/MCP/MCPServerTypeTag.tsx	(date 1755169959552)
@@ -1,32 +1,31 @@
 import {Tag} from '@panda-design/components';
-import {MCPServerType} from '@/types/mcp/mcp';
-import {IconLocal, IconRemote, IconStandard} from '@/icons/mcp';
+import {MCPServerProtocolType} from '@/types/mcp/mcp';
+import {IconLocal, IconRemote} from '@/icons/mcp';
 
-export const SERVER_TYPE_DICT: Record<MCPServerType, string> = {
-    script: 'Local',
-    openapi: 'Remote',
-    external: '标准MCP',
+export const SERVER_TYPE_DICT: Record<MCPServerProtocolType, string> = {
+    SSE: 'remote',
+    STDIO: 'local',
+    Streamable_HTTP: 'remote',
 };
 
-export const getServerTypeText = (type: MCPServerType) => {
-    return SERVER_TYPE_DICT[type] || SERVER_TYPE_DICT.external;
+export const getServerTypeText = (type: MCPServerProtocolType) => {
+    return SERVER_TYPE_DICT[type] || 'remote';
 };
 
-export const getServerTypeIcon = (type: MCPServerType) => {
+export const getServerTypeIcon = (type: MCPServerProtocolType) => {
     switch (type) {
-        case 'script':
+        case 'STDIO':
             return <IconLocal />;
-        case 'openapi':
+        case 'SSE':
+        case 'Streamable_HTTP':
             return <IconRemote />;
-        case 'external':
-            return <IconStandard />;
         default:
-            return <IconStandard />;
+            return <IconRemote />;
     }
 };
 
 interface Props {
-    type: MCPServerType;
+    type: MCPServerProtocolType;
 }
 export default function MCPServerTypeTag({type}: Props) {
     return (
Index: src/comatestack/MCP/MCPApplicationDetail/ServerDescription.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Button, message, Modal} from '@panda-design/components';\nimport {Flex, Space, Switch, Typography} from 'antd';\nimport {Dispatch, SetStateAction, useCallback} from 'react';\nimport {css} from '@emotion/css';\nimport DescriptionItem from '@/design/MCP/MCPDescriptionItem';\nimport {useMCPApplicationId} from '@/components/MCP/hooks';\nimport {loadMCPApplicationInfo} from '@/regions/mcp/mcpApplication';\nimport {MCPApplicationServer} from '@/types/mcp/application';\nimport {apiPutMCPServerEnable} from '@/api/mcp/application';\nimport {IconSubscribe} from '@/icons/mcp';\nimport MCPServerTypeTag from '@/components/MCP/MCPServerTypeTag';\nimport {apiPutApplicationUnsubscribe} from '@/api/mcp';\nimport MCPServerProtocolTypeTag from '@/components/MCP/MCPServerProtocolTypeTag';\nimport TagGroup from '@/components/MCP/TagGroup';\nimport {UserAvatarList} from '@/components/MCP/UserAvatarList';\nimport CollapsePanel from './CollapsePanel';\n\nconst textColorSecondary = css`\n    color: #545454;\n`;\n\n\ninterface Props {\n    server: MCPApplicationServer;\n    setActiveIndex: Dispatch<SetStateAction<string>>;\n}\nconst ServerDescription = ({server, setActiveIndex}: Props) => {\n    const applicationId = useMCPApplicationId();\n\n    const handleChange = useCallback(\n        async (checked: boolean) => Modal.confirm({\n            content: `此操作会在应用下次获取MCP Server时生效，请确认是否${checked ? '打开' : '关闭'}？`,\n            onOk: async () => {\n                try {\n                    await apiPutMCPServerEnable({\n                        applicationId,\n                        serverId: server?.id,\n                        enable: checked,\n                    });\n                    loadMCPApplicationInfo({applicationId});\n                }\n                catch (e) {\n                    message.error('操作失败');\n                    throw e;\n                }\n            },\n        }),\n        [applicationId, server]\n    );\n\n    const handleUnsubscribe = useCallback(\n        () => Modal.confirm({\n            content: '取消订阅后所有配置会被删除，重新订阅需要重新配置。请确认是否取消订阅？',\n            onOk: async () => {\n                try {\n                    await apiPutApplicationUnsubscribe({\n                        applicationId,\n                        serverId: server?.id,\n                    });\n                    setActiveIndex('0');\n                    loadMCPApplicationInfo({applicationId});\n                }\n                catch (e) {\n                    message.error('操作失败');\n                    throw e;\n                }\n            },\n        }),\n        [applicationId, server?.id, setActiveIndex]\n    );\n    return (\n        <CollapsePanel\n            header={(\n                <>{server?.name} MCP Server详情</>\n            )}\n            extra={\n                <Space>\n                    <Button\n                        icon={<IconSubscribe />}\n                        type=\"text\"\n                        onClick={handleUnsubscribe}\n                    >\n                        取消订阅\n                    </Button>\n                    <Flex align=\"center\" gap={4} className={textColorSecondary}>\n                        <Switch\n                            size=\"small\"\n                            checked={server?.enable ?? false}\n                            onChange={handleChange}\n                        />\n                        <span style={{whiteSpace: 'nowrap', color: '#000'}}>生效</span>\n                    </Flex>\n                </Space>\n            }\n            body={(\n                <Flex vertical gap={12}>\n                    <Space size={32}>\n                        <DescriptionItem type=\"secondary\" label=\"类型\">\n                            <MCPServerTypeTag type={server?.serverSourceType} />\n                        </DescriptionItem>\n                        <DescriptionItem type=\"secondary\" label=\"协议\">\n                            <MCPServerProtocolTypeTag type={server?.serverProtocolType} />\n                        </DescriptionItem>\n                        <DescriptionItem type=\"secondary\" label=\"场景\">\n                            <TagGroup\n                                color=\"gray\"\n                                prefix={null}\n                                maxNum={3}\n                                labels={(server?.labels ?? []).map(item => ({id: item.id, label: item.labelValue}))}\n                            />\n                        </DescriptionItem>\n                    </Space>\n                    <Space size={32}>\n                        <DescriptionItem type=\"secondary\" label=\"部门\">\n                            <span>\n                                {server?.departmentName || '暂无部门信息'}\n                            </span>\n                        </DescriptionItem>\n                        <DescriptionItem type=\"secondary\" label=\"最近更新时间\">\n                            <span>{server?.lastModifyTime || '暂无'}</span>\n                        </DescriptionItem>\n                        <DescriptionItem type=\"secondary\" label=\"联系人\">\n                            <UserAvatarList users={server.contacts} max={2} />\n                        </DescriptionItem>\n                    </Space>\n                    <Typography.Paragraph type=\"secondary\">\n                        {server?.description || '暂无描述'}\n                    </Typography.Paragraph>\n                </Flex>\n            )}\n        />\n    );\n};\n\nexport default ServerDescription;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPApplicationDetail/ServerDescription.tsx b/src/comatestack/MCP/MCPApplicationDetail/ServerDescription.tsx
--- a/src/comatestack/MCP/MCPApplicationDetail/ServerDescription.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/comatestack/MCP/MCPApplicationDetail/ServerDescription.tsx	(date 1755169995600)
@@ -96,7 +96,7 @@
                 <Flex vertical gap={12}>
                     <Space size={32}>
                         <DescriptionItem type="secondary" label="类型">
-                            <MCPServerTypeTag type={server?.serverSourceType} />
+                            <MCPServerTypeTag type={server?.serverProtocolType} />
                         </DescriptionItem>
                         <DescriptionItem type="secondary" label="协议">
                             <MCPServerProtocolTypeTag type={server?.serverProtocolType} />
Index: src/components/MCP/MCPServerCard/MCPServerList.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>/* eslint-disable max-lines */\nimport {Flex, List} from 'antd';\nimport {useCallback, useEffect, useLayoutEffect, useMemo, useState} from 'react';\nimport InfiniteScroll from 'react-infinite-scroll-component';\nimport {MCPServerBase} from '@/types/mcp/mcp';\nimport CreateMCPAppModal from '@/components/MCP/CreateMCPAppButton/CreateMCPAppModal';\nimport MCPEmpty from '@/design/MCP/MCPEmpty';\nimport {FilterValues, TabValues} from '../MCPServerFilter';\nimport {ALL_LABELS} from '../MCPServerFilter/LabelsFilterContent';\nimport {useLoadMore} from '../hooks';\nimport MCPServerCard from './MCPServerCard';\nimport {useFetchMCPServersContext} from './FetchMCPServersProvider';\n\nconst PAGE_SIZE = 12;\n\nconst CARD_HEIGHT = 235;\n\nconst processEmptyText = (filterData?: FilterValues & TabValues) => {\n    if (filterData?.keywords?.trim()\n        || !(filterData?.labels?.length === 1 && filterData?.labels[0] === ALL_LABELS)\n        || filterData?.serverSourceType\n        || filterData?.serverProtocolType\n    ) {\n        return '暂无结果';\n    }\n    if (filterData?.favorite) {\n        return '暂无收藏的MCP Server';\n    }\n    if (filterData?.isMine) {\n        return '暂无发布的MCP Server';\n    }\n    return '暂无MCP Server';\n};\n\ninterface Props {\n    searchParams?: FilterValues & TabValues; // Define the type according to your needs\n    scrollableTarget: string;\n}\nconst MCPServerList = ({searchParams, scrollableTarget}: Props) => {\n    const {api: fetchServers} = useFetchMCPServersContext();\n    const [pageSize, setPageSize] = useState(PAGE_SIZE);\n    const api = useCallback(\n        (params: {current: number, limit: number}) => {\n            return fetchServers(params);\n        },\n        [fetchServers]\n    );\n    const {loadMore, total, list, refresh} = useLoadMore<MCPServerBase>(api, pageSize);\n\n    useEffect(\n        () => {\n            refresh();\n        },\n        [refresh]\n    );\n\n    const emptyText = useMemo(\n        () => processEmptyText(searchParams),\n        [searchParams]\n    );\n\n    useLayoutEffect(\n        () => {\n            const container = document.getElementById('scrollableDiv');\n            const height = container?.clientHeight || 0;\n            let rows = Math.ceil(height / CARD_HEIGHT);\n            // 因为每行可以有3个或2个卡片，所以需要确保pagesizepageSize是2和3的公倍数，所以需要调整rows的值为偶数。\n            rows = rows % 2 === 0 ? rows : rows + 1;\n            const maxItems = rows * 3;\n            if (maxItems > PAGE_SIZE) {\n                setPageSize(maxItems);\n            }\n        },\n        []\n    );\n\n    return (\n        <>\n            <InfiniteScroll\n                style={{overflow: 'none'}}\n                dataLength={list.length || 0}\n                next={loadMore}\n                hasMore={total > list.length}\n                loader={<Flex justify=\"center\" align=\"center\"><div>加载中...</div></Flex>}\n                scrollableTarget={scrollableTarget}\n            >\n                {(\n                    <List\n                        grid={{\n                            gutter: 20,\n                            column: 2,\n                            xs: 2,\n                            sm: 3,\n                            md: 3,\n                            lg: 3,\n                            xl: 3,\n                            xxl: 3,\n                        }}\n                        dataSource={list}\n                        rowKey=\"id\"\n                        renderItem={server => (\n                            <List.Item>\n                                <MCPServerCard refresh={refresh} key={server.id} server={server} />\n                            </List.Item>\n                        )}\n                        locale={{\n                            emptyText: (\n                                <MCPEmpty\n                                    description={(\n                                        <Flex justify=\"center\">\n                                            {emptyText}\n                                        </Flex>\n                                    )}\n                                />\n                            ),\n                        }}\n                    />\n                )}\n            </InfiniteScroll>\n            <CreateMCPAppModal />\n        </>\n    );\n};\n\nexport default MCPServerList;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/MCPServerCard/MCPServerList.tsx b/src/components/MCP/MCPServerCard/MCPServerList.tsx
--- a/src/components/MCP/MCPServerCard/MCPServerList.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/components/MCP/MCPServerCard/MCPServerList.tsx	(date 1755159184814)
@@ -2,6 +2,7 @@
 import {Flex, List} from 'antd';
 import {useCallback, useEffect, useLayoutEffect, useMemo, useState} from 'react';
 import InfiniteScroll from 'react-infinite-scroll-component';
+import styled from '@emotion/styled';
 import {MCPServerBase} from '@/types/mcp/mcp';
 import CreateMCPAppModal from '@/components/MCP/CreateMCPAppButton/CreateMCPAppModal';
 import MCPEmpty from '@/design/MCP/MCPEmpty';
@@ -11,6 +12,16 @@
 import MCPServerCard from './MCPServerCard';
 import {useFetchMCPServersContext} from './FetchMCPServersProvider';
 
+const StyledList = styled(List<MCPServerBase>)`
+    .ant-list-grid .ant-col {
+        @media (min-width: 1600px) {
+            width: 25% !important;
+            max-width: 25% !important;
+            flex: 0 0 25% !important;
+        }
+    }
+`;
+
 const PAGE_SIZE = 12;
 
 const CARD_HEIGHT = 235;
@@ -85,7 +96,7 @@
                 scrollableTarget={scrollableTarget}
             >
                 {(
-                    <List
+                    <StyledList
                         grid={{
                             gutter: 20,
                             column: 2,
Index: src/icons/mcp/index.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import type { FC, SVGProps } from \"react\";\nimport { createIcon } from \"@panda-design/components\";\nimport SendActived from \"./SendActived\";\nimport AiTools1 from \"./AiTools1\";\nimport AiTools2 from \"./AiTools2\";\nimport AiTools3 from \"./AiTools3\";\nimport AiTools4 from \"./AiTools4\";\nimport Alert from \"./Alert\";\nimport ArrowRight from \"./ArrowRight\";\nimport Case from \"./Case\";\nimport Code from \"./Code\";\nimport Comment from \"./Comment\";\nimport Copy from \"./Copy\";\nimport Debug from \"./Debug\";\nimport Delete from \"./Delete\";\nimport Detail from \"./Detail\";\nimport Elipsis from \"./Elipsis\";\nimport ExitFullscreen from \"./ExitFullscreen\";\nimport Eye from \"./Eye\";\nimport Fullscreen from \"./Fullscreen\";\nimport Import from \"./Import\";\nimport LeftOutlined from \"./LeftOutlined\";\nimport LightMySpcae from \"./LightMySpcae\";\nimport LightPlayground from \"./LightPlayground\";\nimport List from \"./List\";\nimport Local from \"./Local\";\nimport LocalMcp from \"./LocalMcp\";\nimport Mcp from \"./Mcp\";\nimport McpAvatar from \"./McpAvatar\";\nimport MySpcae from \"./MySpcae\";\nimport OffcialExample from \"./OffcialExample\";\nimport Ops from \"./Ops\";\nimport Organization from \"./Organization\";\nimport Params from \"./Params\";\nimport Playground from \"./Playground\";\nimport PlaygroundConfig from \"./PlaygroundConfig\";\nimport Refresh from \"./Refresh\";\nimport Remote from \"./Remote\";\nimport RemoteMcp from \"./RemoteMcp\";\nimport Result from \"./Result\";\nimport RightArrow from \"./RightArrow\";\nimport Send from \"./Send\";\nimport Setting from \"./Setting\";\nimport ShowMore from \"./ShowMore\";\nimport SortAsc from \"./SortAsc\";\nimport SortDesc from \"./SortDesc\";\nimport Sse from \"./Sse\";\nimport Standard from \"./Standard\";\nimport Stdio from \"./Stdio\";\nimport StdioMcp from \"./StdioMcp\";\nimport Step01 from \"./Step01\";\nimport Step02 from \"./Step02\";\nimport Step03 from \"./Step03\";\nimport Step04 from \"./Step04\";\nimport StopGenerate from \"./StopGenerate\";\nimport Subscribe from \"./Subscribe\";\nimport Subscribe2 from \"./Subscribe2\";\nimport SubscribeFilled from \"./SubscribeFilled\";\nimport Subtract from \"./Subtract\";\nimport Tag from \"./Tag\";\nimport Test from \"./Test\";\nimport Tool from \"./Tool\";\nimport Unfold from \"./Unfold\";\nimport SubscribeBlue from \"./SubscribeBlue\";\n\nexport const IconSendActived = createIcon(SendActived);\nexport const IconAiTools1 = createIcon(AiTools1);\nexport const IconAiTools2 = createIcon(AiTools2);\nexport const IconAiTools3 = createIcon(AiTools3);\nexport const IconAiTools4 = createIcon(AiTools4);\nexport const IconAlert = createIcon(Alert);\nexport const IconArrowRight = createIcon(ArrowRight);\nexport const IconCase = createIcon(Case);\nexport const IconCode = createIcon(Code);\nexport const IconComment = createIcon(Comment);\nexport const IconCopy = createIcon(Copy);\nexport const IconDebug = createIcon(Debug);\nexport const IconDelete = createIcon(Delete);\nexport const IconDetail = createIcon(Detail);\nexport const IconElipsis = createIcon(Elipsis);\nexport const IconExitFullscreen = createIcon(ExitFullscreen);\nexport const IconEye = createIcon(Eye);\nexport const IconFullscreen = createIcon(Fullscreen);\nexport const IconImport = createIcon(Import);\nexport const IconLeftOutlined = createIcon(LeftOutlined);\nexport const IconLightMySpcae = createIcon(LightMySpcae);\nexport const IconLightPlayground = createIcon(LightPlayground);\nexport const IconList = createIcon(List);\nexport const IconLocal = createIcon(Local);\nexport const IconLocalMcp = createIcon(LocalMcp);\nexport const IconMcp = createIcon(Mcp);\nexport const IconMcpAvatar = createIcon(McpAvatar);\nexport const IconMySpcae = createIcon(MySpcae);\nexport const IconOffcialExample = createIcon(OffcialExample);\nexport const IconOps = createIcon(Ops);\nexport const IconOrganization = createIcon(Organization);\nexport const IconParams = createIcon(Params);\nexport const IconPlayground = createIcon(Playground);\nexport const IconPlaygroundConfig = createIcon(PlaygroundConfig);\nexport const IconRefresh = createIcon(Refresh);\nexport const IconRemote = createIcon(Remote);\nexport const IconRemoteMcp = createIcon(RemoteMcp);\nexport const IconResult = createIcon(Result);\nexport const IconRightArrow = createIcon(RightArrow);\nexport const IconSend = createIcon(Send);\nexport const IconSetting = createIcon(Setting);\nexport const IconShowMore = createIcon(ShowMore);\nexport const IconSortAsc = createIcon(SortAsc);\nexport const IconSortDesc = createIcon(SortDesc);\nexport const IconSse = createIcon(Sse);\nexport const IconStandard = createIcon(Standard);\nexport const IconStdio = createIcon(Stdio);\nexport const IconStdioMcp = createIcon(StdioMcp);\nexport const IconStep01 = createIcon(Step01);\nexport const IconStep02 = createIcon(Step02);\nexport const IconStep03 = createIcon(Step03);\nexport const IconStep04 = createIcon(Step04);\nexport const IconStopGenerate = createIcon(StopGenerate);\nexport const IconSubscribe = createIcon(Subscribe);\nexport const IconSubscribeBlue = createIcon(SubscribeBlue);\nexport const IconSubscribe2 = createIcon(Subscribe2);\nexport const IconSubscribeFilled = createIcon(SubscribeFilled);\nexport const IconSubtract = createIcon(Subtract);\nexport const IconTag = createIcon(Tag);\nexport const IconTest = createIcon(Test);\nexport const IconTool = createIcon(Tool);\nexport const IconUnfold = createIcon(Unfold);\n\nexport const iconsMap: Record<string, FC<SVGProps<SVGSVGElement>>> = {\n    SendActived: IconSendActived,\n    aiTools1: IconAiTools1,\n    aiTools2: IconAiTools2,\n    aiTools3: IconAiTools3,\n    aiTools4: IconAiTools4,\n    alert: IconAlert,\n    arrowRight: IconArrowRight,\n    case: IconCase,\n    code: IconCode,\n    comment: IconComment,\n    copy: IconCopy,\n    debug: IconDebug,\n    delete: IconDelete,\n    detail: IconDetail,\n    elipsis: IconElipsis,\n    exitFullscreen: IconExitFullscreen,\n    eye: IconEye,\n    fullscreen: IconFullscreen,\n    import: IconImport,\n    leftOutlined: IconLeftOutlined,\n    lightMySpcae: IconLightMySpcae,\n    lightPlayground: IconLightPlayground,\n    list: IconList,\n    local: IconLocal,\n    localMCP: IconLocalMcp,\n    mcp: IconMcp,\n    mcpAvatar: IconMcpAvatar,\n    mySpcae: IconMySpcae,\n    offcialExample: IconOffcialExample,\n    ops: IconOps,\n    organization: IconOrganization,\n    params: IconParams,\n    playground: IconPlayground,\n    playgroundConfig: IconPlaygroundConfig,\n    refresh: IconRefresh,\n    remote: IconRemote,\n    remoteMCP: IconRemoteMcp,\n    result: IconResult,\n    rightArrow: IconRightArrow,\n    send: IconSend,\n    setting: IconSetting,\n    showMore: IconShowMore,\n    sortAsc: IconSortAsc,\n    sortDesc: IconSortDesc,\n    sse: IconSse,\n    standard: IconStandard,\n    stdio: IconStdio,\n    stdioMCP: IconStdioMcp,\n    step01: IconStep01,\n    step02: IconStep02,\n    step03: IconStep03,\n    step04: IconStep04,\n    stopGenerate: IconStopGenerate,\n    subscribe: IconSubscribe,\n    subscribeBlue: IconSubscribeBlue,\n    subscribe2: IconSubscribe2,\n    subscribeFilled: IconSubscribeFilled,\n    subtract: IconSubtract,\n    tag: IconTag,\n    test: IconTest,\n    tool: IconTool,\n    unfold: IconUnfold,\n};\n\nexport default iconsMap;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/index.ts b/src/icons/mcp/index.ts
--- a/src/icons/mcp/index.ts	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/icons/mcp/index.ts	(date 1755161374109)
@@ -7,13 +7,16 @@
 import AiTools4 from "./AiTools4";
 import Alert from "./Alert";
 import ArrowRight from "./ArrowRight";
+import ArrowRight1 from "./ArrowRight1";
+import CallCount from "./CallCount";
 import Case from "./Case";
-import Code from "./Code";
 import Comment from "./Comment";
 import Copy from "./Copy";
 import Debug from "./Debug";
 import Delete from "./Delete";
 import Detail from "./Detail";
+import Dev from "./Dev";
+import DevBg from "./DevBg";
 import Elipsis from "./Elipsis";
 import ExitFullscreen from "./ExitFullscreen";
 import Eye from "./Eye";
@@ -30,12 +33,14 @@
 import MySpcae from "./MySpcae";
 import OffcialExample from "./OffcialExample";
 import Ops from "./Ops";
+import OpsBg from "./OpsBg";
 import Organization from "./Organization";
 import Params from "./Params";
 import Playground from "./Playground";
 import PlaygroundConfig from "./PlaygroundConfig";
 import Refresh from "./Refresh";
 import Remote from "./Remote";
+import Remote1 from "./Remote1";
 import RemoteMcp from "./RemoteMcp";
 import Result from "./Result";
 import RightArrow from "./RightArrow";
@@ -45,6 +50,7 @@
 import SortAsc from "./SortAsc";
 import SortDesc from "./SortDesc";
 import Sse from "./Sse";
+import Sse1 from "./Sse1";
 import Standard from "./Standard";
 import Stdio from "./Stdio";
 import StdioMcp from "./StdioMcp";
@@ -59,9 +65,10 @@
 import Subtract from "./Subtract";
 import Tag from "./Tag";
 import Test from "./Test";
+import TestBg from "./TestBg";
 import Tool from "./Tool";
 import Unfold from "./Unfold";
-import SubscribeBlue from "./SubscribeBlue";
+import Vip from "./Vip";
 
 export const IconSendActived = createIcon(SendActived);
 export const IconAiTools1 = createIcon(AiTools1);
@@ -70,13 +77,16 @@
 export const IconAiTools4 = createIcon(AiTools4);
 export const IconAlert = createIcon(Alert);
 export const IconArrowRight = createIcon(ArrowRight);
+export const IconArrowRight1 = createIcon(ArrowRight1);
+export const IconCallCount = createIcon(CallCount);
 export const IconCase = createIcon(Case);
-export const IconCode = createIcon(Code);
 export const IconComment = createIcon(Comment);
 export const IconCopy = createIcon(Copy);
 export const IconDebug = createIcon(Debug);
 export const IconDelete = createIcon(Delete);
 export const IconDetail = createIcon(Detail);
+export const IconDev = createIcon(Dev);
+export const IconDevBg = createIcon(DevBg);
 export const IconElipsis = createIcon(Elipsis);
 export const IconExitFullscreen = createIcon(ExitFullscreen);
 export const IconEye = createIcon(Eye);
@@ -93,12 +103,14 @@
 export const IconMySpcae = createIcon(MySpcae);
 export const IconOffcialExample = createIcon(OffcialExample);
 export const IconOps = createIcon(Ops);
+export const IconOpsBg = createIcon(OpsBg);
 export const IconOrganization = createIcon(Organization);
 export const IconParams = createIcon(Params);
 export const IconPlayground = createIcon(Playground);
 export const IconPlaygroundConfig = createIcon(PlaygroundConfig);
 export const IconRefresh = createIcon(Refresh);
 export const IconRemote = createIcon(Remote);
+export const IconRemote1 = createIcon(Remote1);
 export const IconRemoteMcp = createIcon(RemoteMcp);
 export const IconResult = createIcon(Result);
 export const IconRightArrow = createIcon(RightArrow);
@@ -108,6 +120,7 @@
 export const IconSortAsc = createIcon(SortAsc);
 export const IconSortDesc = createIcon(SortDesc);
 export const IconSse = createIcon(Sse);
+export const IconSse1 = createIcon(Sse1);
 export const IconStandard = createIcon(Standard);
 export const IconStdio = createIcon(Stdio);
 export const IconStdioMcp = createIcon(StdioMcp);
@@ -117,14 +130,15 @@
 export const IconStep04 = createIcon(Step04);
 export const IconStopGenerate = createIcon(StopGenerate);
 export const IconSubscribe = createIcon(Subscribe);
-export const IconSubscribeBlue = createIcon(SubscribeBlue);
 export const IconSubscribe2 = createIcon(Subscribe2);
 export const IconSubscribeFilled = createIcon(SubscribeFilled);
 export const IconSubtract = createIcon(Subtract);
 export const IconTag = createIcon(Tag);
 export const IconTest = createIcon(Test);
+export const IconTestBg = createIcon(TestBg);
 export const IconTool = createIcon(Tool);
 export const IconUnfold = createIcon(Unfold);
+export const IconVip = createIcon(Vip);
 
 export const iconsMap: Record<string, FC<SVGProps<SVGSVGElement>>> = {
     SendActived: IconSendActived,
@@ -134,13 +148,16 @@
     aiTools4: IconAiTools4,
     alert: IconAlert,
     arrowRight: IconArrowRight,
+    arrowRight1: IconArrowRight1,
+    callCount: IconCallCount,
     case: IconCase,
-    code: IconCode,
     comment: IconComment,
     copy: IconCopy,
     debug: IconDebug,
     delete: IconDelete,
     detail: IconDetail,
+    dev: IconDev,
+    devBg: IconDevBg,
     elipsis: IconElipsis,
     exitFullscreen: IconExitFullscreen,
     eye: IconEye,
@@ -157,12 +174,14 @@
     mySpcae: IconMySpcae,
     offcialExample: IconOffcialExample,
     ops: IconOps,
+    opsBg: IconOpsBg,
     organization: IconOrganization,
     params: IconParams,
     playground: IconPlayground,
     playgroundConfig: IconPlaygroundConfig,
     refresh: IconRefresh,
     remote: IconRemote,
+    remote1: IconRemote1,
     remoteMCP: IconRemoteMcp,
     result: IconResult,
     rightArrow: IconRightArrow,
@@ -172,6 +191,7 @@
     sortAsc: IconSortAsc,
     sortDesc: IconSortDesc,
     sse: IconSse,
+    sse1: IconSse1,
     standard: IconStandard,
     stdio: IconStdio,
     stdioMCP: IconStdioMcp,
@@ -181,14 +201,15 @@
     step04: IconStep04,
     stopGenerate: IconStopGenerate,
     subscribe: IconSubscribe,
-    subscribeBlue: IconSubscribeBlue,
     subscribe2: IconSubscribe2,
     subscribeFilled: IconSubscribeFilled,
     subtract: IconSubtract,
     tag: IconTag,
     test: IconTest,
+    testBg: IconTestBg,
     tool: IconTool,
     unfold: IconUnfold,
+    vip: IconVip,
 };
 
 export default iconsMap;
Index: src/components/MCP/MCPCollectButton/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {StarFilled, StarOutlined} from '@ant-design/icons';\nimport {Button, ButtonProps, message} from '@panda-design/components';\nimport {MouseEvent, CSSProperties} from 'react';\nimport {apiDeleteServerFavorite, apiPutServerFavorite} from '@/api/mcp';\n\ninterface Props {\n    serverId: number;\n    favorite: boolean;\n    refresh: () => void;\n    size?: ButtonProps['size'];\n    style?: CSSProperties;\n    showText?: boolean;\n    iconColor?: string;\n    className?: string;\n}\n\nexport const MCPCollectButton = ({\n    serverId,\n    refresh,\n    favorite,\n    size,\n    style,\n    showText = true,\n    iconColor,\n    className,\n}: Props) => {\n    const handleClick = async (e: MouseEvent) => {\n        e.stopPropagation();\n        e.preventDefault();\n        if (favorite) {\n            await apiDeleteServerFavorite({mcpServerId: serverId});\n        } else {\n            await apiPutServerFavorite({mcpServerId: serverId});\n        }\n        message.success(favorite ? '取消收藏成功' : '收藏成功');\n        refresh();\n    };\n    return (\n        <Button\n            onClick={handleClick}\n            type=\"text\"\n            size={size}\n            style={style}\n            className={className}\n            icon={\n                favorite\n                    ? <StarFilled style={{color: '#FFA400'}} />\n                    : <StarOutlined style={{color: iconColor}} />\n            }\n        >\n            {showText ? '收藏' : undefined}\n        </Button>\n    );\n};\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/MCPCollectButton/index.tsx b/src/components/MCP/MCPCollectButton/index.tsx
--- a/src/components/MCP/MCPCollectButton/index.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/components/MCP/MCPCollectButton/index.tsx	(date 1755162926626)
@@ -10,8 +10,6 @@
     size?: ButtonProps['size'];
     style?: CSSProperties;
     showText?: boolean;
-    iconColor?: string;
-    className?: string;
 }
 
 export const MCPCollectButton = ({
@@ -21,8 +19,6 @@
     size,
     style,
     showText = true,
-    iconColor,
-    className,
 }: Props) => {
     const handleClick = async (e: MouseEvent) => {
         e.stopPropagation();
@@ -41,11 +37,10 @@
             type="text"
             size={size}
             style={style}
-            className={className}
             icon={
                 favorite
                     ? <StarFilled style={{color: '#FFA400'}} />
-                    : <StarOutlined style={{color: iconColor}} />
+                    : <StarOutlined />
             }
         >
             {showText ? '收藏' : undefined}
Index: src/icons/mcp/ops.svg
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path d=\"M1.94721 1.94644C3.05247 1.26436 4.52657 1.632 5.15815 2.78628C5.842 3.88805 5.47312 5.35695 4.31538 5.98648C3.73649 6.30114 3.05243 6.35381 2.47358 6.19644C1.2105 8.6099 1.21089 11.6527 2.68452 14.171C4.52662 17.3191 8.10526 18.8407 11.5263 18.2111C11.9473 18.1587 12.3156 18.3688 12.4208 18.841C12.5261 19.3132 12.2627 19.7331 11.789 19.838C7.7365 20.5724 3.4732 18.7884 1.31538 15.0109C-0.474039 11.9153 -0.368047 8.24201 1.21088 5.30386C1.15828 5.2514 1.15804 5.19909 1.10541 5.14663C0.421318 4.04485 0.78941 2.57604 1.94721 1.94644ZM8.21088 0.162258C12.2107 -0.572061 16.4741 1.2119 18.6845 4.98941C20.4739 8.08497 20.3679 11.7583 18.789 14.6964C18.8416 14.7488 18.842 14.8008 18.8945 14.8009C19.5786 15.9027 19.2104 17.3714 18.0527 18.0011C16.9474 18.6832 15.4733 18.3165 14.8417 17.1623C14.1575 16.0604 14.5266 14.5907 15.6845 13.9611C16.2633 13.6465 16.9475 13.5938 17.5263 13.7511C18.7893 11.3377 18.789 8.29481 17.3154 5.77652C15.4732 2.62858 11.8946 1.10688 8.47358 1.73648C8.05253 1.78894 7.68431 1.5266 7.57905 1.15933C7.47381 0.687138 7.73721 0.267189 8.21088 0.162258ZM17.5263 15.6408C17.3158 15.2735 16.842 15.1682 16.4736 15.3781C16.1052 15.588 16.0004 16.0606 16.2109 16.4279C16.4215 16.7947 16.8944 16.8992 17.2626 16.6896C17.631 16.4798 17.7366 16.008 17.5263 15.6408ZM9.73627 4.08511C9.89412 3.97232 10.1048 3.97239 10.2626 4.08511L12.6318 5.55191C12.7894 5.66466 12.8943 5.8335 12.8945 6.05874V8.03335C12.8945 8.25899 13.0003 8.42834 13.1581 8.54116L14.7363 9.50015C14.8942 9.61297 14.9999 9.78232 14.9999 10.008V12.9416C14.9998 13.167 14.8941 13.3366 14.7363 13.4494L12.3681 14.9152C12.2102 15.028 11.9996 15.028 11.8417 14.9152L9.99995 13.7873L8.15815 14.9152C8.00026 15.028 7.78968 15.028 7.63178 14.9152L5.26264 13.4494C5.10487 13.3366 5.00005 13.167 4.99995 12.9416V10.008C4.99995 9.78245 5.1049 9.61297 5.26264 9.50015L6.84174 8.54116C6.99964 8.42834 7.10541 8.25899 7.10541 8.03335V6.05874C7.10554 5.83348 7.21051 5.66466 7.36811 5.55191L9.73627 4.08511ZM6.57905 10.6847V12.3205L7.89448 13.1105L9.21088 12.3205V10.6847L7.89448 9.89468L6.57905 10.6847ZM10.789 10.6847V12.3205L12.1054 13.1105L13.4208 12.3205V10.6847L12.1054 9.89468L10.789 10.6847ZM8.68452 6.73648V8.37222L9.99995 9.16128L11.3154 8.37222V6.73648L9.99995 5.94644L8.68452 6.73648ZM3.78901 3.52066C3.57848 3.15339 3.10469 3.04809 2.73627 3.25796C2.3681 3.46792 2.26311 3.94059 2.47358 4.30777C2.68417 4.67486 3.15797 4.77931 3.52631 4.56948C3.89458 4.35956 3.9995 3.88786 3.78901 3.52066Z\" fill=\"black\"/>\n</svg>\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/ops.svg b/src/icons/mcp/ops.svg
--- a/src/icons/mcp/ops.svg	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/icons/mcp/ops.svg	(date 1755156351169)
@@ -1,3 +1,15 @@
-<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
-<path d="M1.94721 1.94644C3.05247 1.26436 4.52657 1.632 5.15815 2.78628C5.842 3.88805 5.47312 5.35695 4.31538 5.98648C3.73649 6.30114 3.05243 6.35381 2.47358 6.19644C1.2105 8.6099 1.21089 11.6527 2.68452 14.171C4.52662 17.3191 8.10526 18.8407 11.5263 18.2111C11.9473 18.1587 12.3156 18.3688 12.4208 18.841C12.5261 19.3132 12.2627 19.7331 11.789 19.838C7.7365 20.5724 3.4732 18.7884 1.31538 15.0109C-0.474039 11.9153 -0.368047 8.24201 1.21088 5.30386C1.15828 5.2514 1.15804 5.19909 1.10541 5.14663C0.421318 4.04485 0.78941 2.57604 1.94721 1.94644ZM8.21088 0.162258C12.2107 -0.572061 16.4741 1.2119 18.6845 4.98941C20.4739 8.08497 20.3679 11.7583 18.789 14.6964C18.8416 14.7488 18.842 14.8008 18.8945 14.8009C19.5786 15.9027 19.2104 17.3714 18.0527 18.0011C16.9474 18.6832 15.4733 18.3165 14.8417 17.1623C14.1575 16.0604 14.5266 14.5907 15.6845 13.9611C16.2633 13.6465 16.9475 13.5938 17.5263 13.7511C18.7893 11.3377 18.789 8.29481 17.3154 5.77652C15.4732 2.62858 11.8946 1.10688 8.47358 1.73648C8.05253 1.78894 7.68431 1.5266 7.57905 1.15933C7.47381 0.687138 7.73721 0.267189 8.21088 0.162258ZM17.5263 15.6408C17.3158 15.2735 16.842 15.1682 16.4736 15.3781C16.1052 15.588 16.0004 16.0606 16.2109 16.4279C16.4215 16.7947 16.8944 16.8992 17.2626 16.6896C17.631 16.4798 17.7366 16.008 17.5263 15.6408ZM9.73627 4.08511C9.89412 3.97232 10.1048 3.97239 10.2626 4.08511L12.6318 5.55191C12.7894 5.66466 12.8943 5.8335 12.8945 6.05874V8.03335C12.8945 8.25899 13.0003 8.42834 13.1581 8.54116L14.7363 9.50015C14.8942 9.61297 14.9999 9.78232 14.9999 10.008V12.9416C14.9998 13.167 14.8941 13.3366 14.7363 13.4494L12.3681 14.9152C12.2102 15.028 11.9996 15.028 11.8417 14.9152L9.99995 13.7873L8.15815 14.9152C8.00026 15.028 7.78968 15.028 7.63178 14.9152L5.26264 13.4494C5.10487 13.3366 5.00005 13.167 4.99995 12.9416V10.008C4.99995 9.78245 5.1049 9.61297 5.26264 9.50015L6.84174 8.54116C6.99964 8.42834 7.10541 8.25899 7.10541 8.03335V6.05874C7.10554 5.83348 7.21051 5.66466 7.36811 5.55191L9.73627 4.08511ZM6.57905 10.6847V12.3205L7.89448 13.1105L9.21088 12.3205V10.6847L7.89448 9.89468L6.57905 10.6847ZM10.789 10.6847V12.3205L12.1054 13.1105L13.4208 12.3205V10.6847L12.1054 9.89468L10.789 10.6847ZM8.68452 6.73648V8.37222L9.99995 9.16128L11.3154 8.37222V6.73648L9.99995 5.94644L8.68452 6.73648ZM3.78901 3.52066C3.57848 3.15339 3.10469 3.04809 2.73627 3.25796C2.3681 3.46792 2.26311 3.94059 2.47358 4.30777C2.68417 4.67486 3.15797 4.77931 3.52631 4.56948C3.89458 4.35956 3.9995 3.88786 3.78901 3.52066Z" fill="black"/>
+<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
+<g clip-path="url(#clip0_1564_25996)">
+<path d="M3.22949 3.22803C4.57048 2.40048 6.35866 2.84617 7.125 4.24658C7.95513 5.58338 7.50827 7.36543 6.10352 8.12939C5.40107 8.51135 4.57061 8.57526 3.86816 8.38428C2.33556 11.3126 2.33598 15.0054 4.12402 18.061C6.35913 21.8804 10.7018 23.7263 14.8525 22.9624C15.3631 22.8989 15.8097 23.1536 15.9375 23.7261C16.0652 24.2989 15.7464 24.8086 15.1719 24.936C10.2549 25.8272 5.08214 23.6628 2.46387 19.0796C0.292673 15.3237 0.420177 10.8672 2.33594 7.30225C2.27208 7.23859 2.27187 7.1745 2.20801 7.11084C1.37823 5.77413 1.82497 3.99196 3.22949 3.22803ZM10.8291 1.06396C15.6824 0.172731 20.855 2.33692 23.5371 6.92041C25.7082 10.6763 25.5808 15.1329 23.665 18.6978C23.7284 18.7611 23.7287 18.8244 23.792 18.8247C24.6221 20.1615 24.1754 21.9436 22.7705 22.7075C21.4295 23.5351 19.6413 23.0895 18.875 21.689C18.0452 20.3522 18.4927 18.57 19.8975 17.8062C20.5998 17.4244 21.4296 17.3604 22.1318 17.5513C23.6644 14.6231 23.6648 10.9311 21.877 7.87549C19.6419 4.05595 15.2992 2.20929 11.1484 2.97314C10.6376 3.0368 10.1902 2.71857 10.0625 2.27295C9.93493 1.70013 10.2545 1.19128 10.8291 1.06396ZM22.1318 19.8433C21.8764 19.3977 21.3015 19.2703 20.8545 19.5249C20.4077 19.7796 20.2798 20.3528 20.5352 20.7983C20.7905 21.2438 21.3655 21.3711 21.8125 21.1167C22.2595 20.8621 22.3873 20.2889 22.1318 19.8433ZM12.6807 5.82275C12.8722 5.68588 13.1278 5.68591 13.3193 5.82275L16.1934 7.60205C16.3849 7.73894 16.5127 7.94453 16.5127 8.21826V10.6138C16.5128 10.8874 16.6405 11.0931 16.832 11.23L18.748 12.3931C18.9395 12.53 19.0664 12.7356 19.0664 13.0093V16.5679C19.0664 16.8415 18.9394 17.0472 18.748 17.1841L15.874 18.9634C15.6824 19.1003 15.4269 19.1003 15.2354 18.9634L13 17.5952L10.7656 18.9634C10.5741 19.1003 10.3185 19.1002 10.127 18.9634L7.25293 17.1841C7.0614 17.0472 6.93359 16.8416 6.93359 16.5679V13.0093C6.93359 12.7355 7.06137 12.53 7.25293 12.3931L9.16895 11.23C9.36048 11.0931 9.48822 10.8874 9.48828 10.6138V8.21826C9.48828 7.94449 9.61604 7.73894 9.80762 7.60205L12.6807 5.82275ZM8.84961 13.8306V15.8149L10.4463 16.7739L12.042 15.8149V13.8306L10.4463 12.8726L8.84961 13.8306ZM13.958 13.8306V15.8149L15.5547 16.7739L17.1514 15.8149V13.8306L15.5547 12.8726L13.958 13.8306ZM11.4043 9.03955V11.0239L13 11.9829L14.5967 11.0239V9.03955L13 8.08154L11.4043 9.03955ZM5.46484 5.13818C5.20941 4.69257 4.63451 4.56519 4.1875 4.81982C3.74091 5.07447 3.61315 5.64684 3.86816 6.09229C4.1236 6.5379 4.6985 6.66528 5.14551 6.41064C5.5924 6.15603 5.7201 5.58375 5.46484 5.13818Z" fill="url(#paint0_linear_1564_25996)"/>
+</g>
+<defs>
+<linearGradient id="paint0_linear_1564_25996" x1="2.08052" y1="4.50655" x2="25.1332" y2="20.28" gradientUnits="userSpaceOnUse">
+<stop stop-color="#6DA6FF"/>
+<stop offset="0.5" stop-color="#BBD4FF"/>
+<stop offset="1" stop-color="#7E7DFF"/>
+</linearGradient>
+<clipPath id="clip0_1564_25996">
+<rect width="26" height="26" fill="white"/>
+</clipPath>
+</defs>
 </svg>
Index: src/types/mcp/mcp.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Path} from '@panda-design/path-form';\nimport type {JSONSchema7} from 'json-schema';\nimport {ChatMessage} from '../staff/chat';\n\n/* eslint-disable max-lines */\nexport interface MCPSpaceMember {\n    label: string;\n    value: string;\n    role: string;\n    nameType: string;\n}\n\nexport interface MCPSpace {\n    id: number;\n    name?: string;\n    description?: string;\n    cloudAccountIds?: string[];\n    member?: MCPSpaceMember[];\n}\n\nexport interface MCPServerParam {\n    name: string;\n    description: string;\n    dataType: string;\n    required: boolean;\n    value: string;\n    defaultValue?: string;\n}\n\nexport interface MCPServerTool {\n    id: number;\n    name: string;\n    description: string;\n    status: string;\n}\n\nexport interface MCPToolItem {\n    id: number;\n    name: string;\n    toolKey: string;\n    serviceId: number;\n    toolStatus: string;\n    description: string;\n    toolParams: {\n        toolParams: BaseParam[];\n        serverParams: BaseParam[];\n    };\n    toolConf: {\n        responseTemplate: string;\n        apiDefinition: APIInfo;\n        openapiConf: ApiDefinition;\n    };\n}\n\nexport interface APIInfo {\n    endpoint: string;\n    params: any;\n    systemParams: BaseParam[];\n}\n\nexport interface ServerConf {\n    serverSourceType: string;\n    serverConfig: string;\n    overview: string;\n    // 是个对象\n    serverExtension: any;\n    serverParams: ServerParam[];\n}\n\nexport interface ServerParam {\n    name: string;\n    type: string;\n    description: string;\n    defaultValue: string;\n    isHide: boolean;\n    required: boolean;\n}\n\nexport interface Visibility {\n    type: string;\n    content: string;\n}\n\nexport interface BaseParam {\n    name: string;\n    example?: string;\n    exampleValue?: string;\n    description?: string;\n    relateApiParamPath?: string;\n    type?: string;\n    refParam?: string;\n    dataType?: string;\n    required: boolean;\n    value?: string; // 输入值时\n    children?: BaseParam[]; // body时\n    key?: string; // 记录 paramList 中的 refParams\n    fieldPath?: Path; // 记录路径\n    canRelate: boolean; // 是否可以关联\n}\n\nexport type MCPServerStatus = 'draft' | 'release';\nexport type MCPServerType = 'openapi' | 'script' | 'external';\nexport type MCPServerProtocolType = 'SSE' | 'STDIO' | 'Streamable_HTTP';\nexport type MCPServerPublishType = [] | ['workspace'] | ['hub'];\n\nexport interface MCPServerBase {\n    id: number;\n    workspaceId: number;\n    viewCount: number;\n    favorite: boolean;\n    name: string;\n    serverKey: string;\n    description: string;\n    serverSourceType: MCPServerType;\n    serverProtocolType: MCPServerProtocolType;\n    serverStatus: MCPServerStatus;\n    serverConf: ServerConf;\n    serverParams: null | MCPServerParam[];\n    serverPublishType: MCPServerPublishType;\n    labels?: SpaceLabel[];\n    icon: string | null;\n    lastModifyTime: string;\n    lastModifyUser: string;\n    publishTime: string;\n    publishUser: string;\n    /* 官方示例的server包括下边两个字段 */\n    officialExample?: boolean;\n    originalId?: number;\n    /* 后端缺失 */\n    departmentName: string;\n    official?: boolean;\n    contacts: string[];\n}\n\nexport interface SpaceLabel {\n    id: number;\n    labelValue: string;\n    labelType: string;\n    content?: string;\n}\n\nexport interface RequestBody {\n    type: string;\n    parameters: any[];\n    jsonSchema: JSONSchema;\n    example: string;\n}\n\nexport interface JSONSchema {\n    type: string;\n    items: JSONSchema | {type: string};\n    properties: Record<string, Description | JSONSchema>;\n    required: string[];\n    description?: string;\n    name?: string;\n    'x-iapi-orders': string[];\n    'x-iapi-ignore-properties': any[];\n}\n\nexport interface Description {\n    type: string;\n    description?: string;\n    examples?: string[];\n    format?: string;\n    enum?: string[];\n    value?: string;\n}\n\ninterface Response {\n    id?: string;\n    code?: number;\n    contentType?: string;\n    jsonSchema?: JSONSchema7;\n    name?: string;\n}\n\ninterface responseExamples {\n    data?: string;\n    name?: string;\n    responseId?: string;\n}\n\nexport interface ApiDefinition {\n    name: string;\n    type: string;\n    path: string;\n    method: string;\n    description: string;\n    operation_id: string;\n    tags: string[];\n    auth: Record<string, unknown>;\n    parameters: Record<string, BaseParam[]>;\n    requestBody: RequestBody;\n    responses: Response[];\n    responseExamples: responseExamples[];\n    fileName?: string;\n}\n\nexport interface ApplicationBase {\n    applicationId: number;\n    applicationName: string;\n    ifSub: boolean;\n}\n\nexport interface AppListForMCPServerBase {\n    workspaceId: number;\n    workspaceName: string;\n    role: string;\n    applications: ApplicationBase[];\n}\n\nexport type MCPChatStatus = 'loading' | 'success' | 'error';\nexport type MCPMessageRole = 'user' | 'assistant' | 'system';\nexport type MCPToolCallStatus = 'success' | 'pending' | 'fail' | 'running';\n\nexport interface MCPToolInfo {\n    type: 'mcpTool';\n    serverName: string;\n    toolName: string;\n}\n\nexport interface MCPToolOutput {\n    mimeType: string;\n    content: string;\n}\n\nexport interface MCPToolCall {\n    type: 'toolCall';\n    tool: MCPToolInfo;\n    input: string; // stringified JSON\n    status: MCPToolCallStatus;\n    output: MCPToolOutput[];\n}\n\nexport interface MCPChatConfig {\n    modelId: string;\n    systemPrompt: string;\n}\n\nexport interface MCPChatSession {\n    messages: ChatMessage[];\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/types/mcp/mcp.ts b/src/types/mcp/mcp.ts
--- a/src/types/mcp/mcp.ts	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/types/mcp/mcp.ts	(date 1755157453099)
@@ -103,10 +103,15 @@
 export type MCPServerProtocolType = 'SSE' | 'STDIO' | 'Streamable_HTTP';
 export type MCPServerPublishType = [] | ['workspace'] | ['hub'];
 
+export interface MCPServerMetrics {
+    viewCount: number;
+    callCount: number;
+}
+
 export interface MCPServerBase {
     id: number;
     workspaceId: number;
-    viewCount: number;
+    serverMetrics: MCPServerMetrics;
     favorite: boolean;
     name: string;
     serverKey: string;
Index: src/design/MCP/MCPCard.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import styled from '@emotion/styled';\nimport {Flex, FlexProps} from 'antd';\nimport bg from '@/assets/mcp/cardBg.png';\nimport vipbg from '@/assets/mcp/cardVipBg.png';\n\nconst Container = styled(Flex)<{official?: boolean}>`\n    position: relative;\n    cursor: pointer;\n    background: url(${({official}) => (official ? vipbg : bg)}) no-repeat;\n    border: 1px solid rgba(75, 108, 159, 0.15);\n    border-radius: 6px;\n    color: #545454;\n    overflow: hidden;\n    \n    :hover {\n        border-color: #0080FF;\n    }\n`;\n\ninterface Props extends FlexProps {\n    official?: boolean;\n}\nexport default function MCPCard({children, onClick, style, className, vertical, official}: Props) {\n    return (\n        <Container\n            onClick={onClick}\n            vertical={vertical}\n            style={style}\n            className={className}\n            official={official}\n        >\n            {children}\n        </Container>\n    );\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/design/MCP/MCPCard.tsx b/src/design/MCP/MCPCard.tsx
--- a/src/design/MCP/MCPCard.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/design/MCP/MCPCard.tsx	(date 1755162127553)
@@ -7,6 +7,7 @@
     position: relative;
     cursor: pointer;
     background: url(${({official}) => (official ? vipbg : bg)}) no-repeat;
+    background-size: cover;
     border: 1px solid rgba(75, 108, 159, 0.15);
     border-radius: 6px;
     color: #545454;
Index: src/icons/mcp/comment.svg
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path d=\"M11.6668 1.16602C12.6334 1.16602 13.4168 1.96927 13.4168 2.96093V9.54152C13.4168 10.5332 12.6334 11.3364 11.6668 11.3364H6.84558L4.05258 12.7685C3.68508 12.9569 3.25633 12.7073 3.21258 12.3024L3.2085 12.2336V11.3364H2.3335C1.40133 11.3364 0.639496 10.5892 0.586413 9.6471L0.583496 9.54152V2.96093C0.583496 1.96927 1.36691 1.16602 2.3335 1.16602H11.6668ZM11.6668 2.36243H2.3335C2.0115 2.36243 1.75016 2.63077 1.75016 2.96093V9.54152C1.75016 9.87168 2.0115 10.14 2.3335 10.14H3.79183C4.11383 10.14 4.37516 10.4078 4.37516 10.7379V11.2653L6.44775 10.203C6.50811 10.1721 6.57349 10.1522 6.64083 10.1441L6.7085 10.14H11.6668C11.9888 10.14 12.2502 9.87168 12.2502 9.54152V2.96093C12.2502 2.63018 11.9888 2.36243 11.6668 2.36243ZM4.0835 5.20443C4.38275 5.20443 4.6295 5.43543 4.66275 5.73293L4.66683 5.80293V6.7001C4.66683 7.03027 4.4055 7.29802 4.0835 7.29802C3.93892 7.29635 3.8 7.24158 3.69318 7.14412C3.58637 7.04667 3.51912 6.91334 3.50425 6.76952L3.50016 6.69952V5.80293C3.50016 5.47277 3.7615 5.20502 4.0835 5.20502V5.20443ZM7.00016 5.20443C7.29941 5.20443 7.54616 5.43543 7.57941 5.73293L7.5835 5.80293V6.7001C7.5835 7.03027 7.32216 7.29802 7.00016 7.29802C6.85558 7.29635 6.71666 7.24158 6.60985 7.14412C6.50304 7.04667 6.43579 6.91334 6.42091 6.76952L6.41683 6.69952V5.80293C6.41683 5.47277 6.67816 5.20502 7.00016 5.20502V5.20443ZM9.91683 5.20443C10.2161 5.20443 10.4628 5.43543 10.4961 5.73293L10.5002 5.80293V6.7001C10.5002 7.03027 10.2388 7.29802 9.91683 7.29802C9.77225 7.29635 9.63333 7.24158 9.52652 7.14412C9.4197 7.04667 9.35246 6.91334 9.33758 6.76952L9.3335 6.69952V5.80293C9.3335 5.47277 9.59483 5.20502 9.91683 5.20502V5.20443Z\" fill=\"black\"/>\n</svg>\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/comment.svg b/src/icons/mcp/comment.svg
--- a/src/icons/mcp/comment.svg	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/icons/mcp/comment.svg	(date 1755160347912)
@@ -1,3 +1,4 @@
-<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
-<path d="M11.6668 1.16602C12.6334 1.16602 13.4168 1.96927 13.4168 2.96093V9.54152C13.4168 10.5332 12.6334 11.3364 11.6668 11.3364H6.84558L4.05258 12.7685C3.68508 12.9569 3.25633 12.7073 3.21258 12.3024L3.2085 12.2336V11.3364H2.3335C1.40133 11.3364 0.639496 10.5892 0.586413 9.6471L0.583496 9.54152V2.96093C0.583496 1.96927 1.36691 1.16602 2.3335 1.16602H11.6668ZM11.6668 2.36243H2.3335C2.0115 2.36243 1.75016 2.63077 1.75016 2.96093V9.54152C1.75016 9.87168 2.0115 10.14 2.3335 10.14H3.79183C4.11383 10.14 4.37516 10.4078 4.37516 10.7379V11.2653L6.44775 10.203C6.50811 10.1721 6.57349 10.1522 6.64083 10.1441L6.7085 10.14H11.6668C11.9888 10.14 12.2502 9.87168 12.2502 9.54152V2.96093C12.2502 2.63018 11.9888 2.36243 11.6668 2.36243ZM4.0835 5.20443C4.38275 5.20443 4.6295 5.43543 4.66275 5.73293L4.66683 5.80293V6.7001C4.66683 7.03027 4.4055 7.29802 4.0835 7.29802C3.93892 7.29635 3.8 7.24158 3.69318 7.14412C3.58637 7.04667 3.51912 6.91334 3.50425 6.76952L3.50016 6.69952V5.80293C3.50016 5.47277 3.7615 5.20502 4.0835 5.20502V5.20443ZM7.00016 5.20443C7.29941 5.20443 7.54616 5.43543 7.57941 5.73293L7.5835 5.80293V6.7001C7.5835 7.03027 7.32216 7.29802 7.00016 7.29802C6.85558 7.29635 6.71666 7.24158 6.60985 7.14412C6.50304 7.04667 6.43579 6.91334 6.42091 6.76952L6.41683 6.69952V5.80293C6.41683 5.47277 6.67816 5.20502 7.00016 5.20502V5.20443ZM9.91683 5.20443C10.2161 5.20443 10.4628 5.43543 10.4961 5.73293L10.5002 5.80293V6.7001C10.5002 7.03027 10.2388 7.29802 9.91683 7.29802C9.77225 7.29635 9.63333 7.24158 9.52652 7.14412C9.4197 7.04667 9.35246 6.91334 9.33758 6.76952L9.3335 6.69952V5.80293C9.3335 5.47277 9.59483 5.20502 9.91683 5.20502V5.20443Z" fill="black"/>
+<svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
+<path d="M8.7998 2.68286C9.18954 2.68286 9.50586 2.99918 9.50586 3.38892V8.83813C9.50577 9.2278 9.18949 9.54321 8.7998 9.54321H6.67969C6.60705 9.5433 6.53771 9.57292 6.48633 9.62427L5.19336 10.9163L3.90137 9.62427C3.85001 9.57292 3.78062 9.54334 3.70801 9.54321H1.58691C1.19723 9.54321 0.880946 9.2278 0.880859 8.83813V3.38892C0.880859 2.99918 1.19718 2.68286 1.58691 2.68286H8.7998ZM1.93945 3.46704C1.78789 3.46704 1.66504 3.58989 1.66504 3.74146V8.4856C1.66528 8.63695 1.78804 8.76001 1.93945 8.76001H3.85352C4.0406 8.76001 4.22021 8.83382 4.35254 8.96606L5.19336 9.80688L6.03516 8.96606C6.16747 8.83388 6.34714 8.76001 6.53418 8.76001H8.44727C8.59868 8.76001 8.72143 8.63695 8.72168 8.4856V3.74146C8.72168 3.58989 8.59883 3.46704 8.44727 3.46704H1.93945ZM3.42871 5.62329C3.75346 5.62329 4.01753 5.88644 4.01758 6.21118C4.01758 6.53596 3.75349 6.79907 3.42871 6.79907C3.10413 6.79883 2.84082 6.53581 2.84082 6.21118C2.84087 5.88659 3.10417 5.62353 3.42871 5.62329ZM5.19336 5.62329C5.51811 5.62329 5.7812 5.88644 5.78125 6.21118C5.78125 6.53596 5.51814 6.79907 5.19336 6.79907C4.86858 6.79907 4.60547 6.53596 4.60547 6.21118C4.60552 5.88644 4.86861 5.62329 5.19336 5.62329ZM6.95801 5.62329C7.28255 5.62353 7.54585 5.88659 7.5459 6.21118C7.5459 6.53581 7.28258 6.79883 6.95801 6.79907C6.63323 6.79907 6.36914 6.53596 6.36914 6.21118C6.36919 5.88644 6.63326 5.62329 6.95801 5.62329Z" fill="#545454"/>
+<path d="M10.3311 7.16248V2.75623C10.3311 2.24881 9.91964 1.8374 9.41223 1.8374H3.97473C3.75727 1.8374 3.58105 1.66119 3.58105 1.44373C3.58105 1.22626 3.75727 1.05005 3.97473 1.05005H9.41223C10.3546 1.05005 11.1184 1.81389 11.1184 2.75623V7.16248C11.1184 7.37994 10.9422 7.55615 10.7247 7.55615C10.5073 7.55615 10.3311 7.37994 10.3311 7.16248Z" fill="#545454"/>
 </svg>
Index: src/icons/mcp/Comment.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import type { SVGProps } from \"react\";\nconst SvgComment = (props: SVGProps<SVGSVGElement>) => (\n    <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"1em\"\n        height=\"1em\"\n        fill=\"none\"\n        viewBox=\"0 0 14 14\"\n        {...props}\n    >\n        <path\n            fill=\"#000\"\n            d=\"M11.667 1.166c.966 0 1.75.803 1.75 1.795v6.58c0 .992-.784 1.795-1.75 1.795H6.846l-2.793 1.432a.582.582 0 0 1-.84-.466l-.005-.068v-.898h-.875c-.932 0-1.694-.747-1.747-1.689l-.003-.105V2.96c0-.992.784-1.795 1.75-1.795zm0 1.196H2.334a.59.59 0 0 0-.584.599v6.58c0 .33.261.599.583.599h1.459a.59.59 0 0 1 .583.598v.527l2.073-1.062q.09-.045.193-.059l.067-.004h4.959a.59.59 0 0 0 .583-.598V2.96a.59.59 0 0 0-.583-.599M4.084 5.204c.299 0 .546.231.579.529l.004.07V6.7a.59.59 0 0 1-.583.598.59.59 0 0 1-.58-.528L3.5 6.7v-.897a.59.59 0 0 1 .583-.598m2.916 0c.3 0 .546.231.58.529l.003.07V6.7A.59.59 0 0 1 7 7.298a.59.59 0 0 1-.58-.528l-.003-.07v-.897A.59.59 0 0 1 7 5.205m2.917 0c.3 0 .546.231.58.529l.003.07V6.7c0 .33-.261.598-.583.598a.59.59 0 0 1-.58-.528l-.003-.07v-.897a.59.59 0 0 1 .583-.598\"\n        />\n    </svg>\n);\nexport default SvgComment;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/Comment.tsx b/src/icons/mcp/Comment.tsx
--- a/src/icons/mcp/Comment.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/icons/mcp/Comment.tsx	(date 1755161373847)
@@ -5,12 +5,16 @@
         width="1em"
         height="1em"
         fill="none"
-        viewBox="0 0 14 14"
+        viewBox="0 0 12 12"
         {...props}
     >
         <path
-            fill="#000"
-            d="M11.667 1.166c.966 0 1.75.803 1.75 1.795v6.58c0 .992-.784 1.795-1.75 1.795H6.846l-2.793 1.432a.582.582 0 0 1-.84-.466l-.005-.068v-.898h-.875c-.932 0-1.694-.747-1.747-1.689l-.003-.105V2.96c0-.992.784-1.795 1.75-1.795zm0 1.196H2.334a.59.59 0 0 0-.584.599v6.58c0 .33.261.599.583.599h1.459a.59.59 0 0 1 .583.598v.527l2.073-1.062q.09-.045.193-.059l.067-.004h4.959a.59.59 0 0 0 .583-.598V2.96a.59.59 0 0 0-.583-.599M4.084 5.204c.299 0 .546.231.579.529l.004.07V6.7a.59.59 0 0 1-.583.598.59.59 0 0 1-.58-.528L3.5 6.7v-.897a.59.59 0 0 1 .583-.598m2.916 0c.3 0 .546.231.58.529l.003.07V6.7A.59.59 0 0 1 7 7.298a.59.59 0 0 1-.58-.528l-.003-.07v-.897A.59.59 0 0 1 7 5.205m2.917 0c.3 0 .546.231.58.529l.003.07V6.7c0 .33-.261.598-.583.598a.59.59 0 0 1-.58-.528l-.003-.07v-.897a.59.59 0 0 1 .583-.598"
+            fill="#545454"
+            d="M8.8 2.683c.39 0 .706.316.706.706v5.45c0 .389-.317.704-.706.704H6.68a.27.27 0 0 0-.194.081l-1.293 1.292-1.292-1.292a.28.28 0 0 0-.193-.08H1.587a.706.706 0 0 1-.706-.706v-5.45c0-.389.316-.705.706-.705zm-6.86.784a.274.274 0 0 0-.275.274v4.745c0 .15.123.274.274.274h1.915c.187 0 .366.074.499.206l.84.84.842-.84a.7.7 0 0 1 .5-.206h1.912a.275.275 0 0 0 .275-.274V3.74a.274.274 0 0 0-.275-.274zm1.489 2.156a.589.589 0 1 1 0 1.178.589.589 0 0 1 0-1.178m1.764 0a.588.588 0 1 1 0 1.176.588.588 0 0 1 0-1.176m1.765 0a.588.588 0 1 1 0 1.177.588.588 0 0 1 0-1.177"
+        />
+        <path
+            fill="#545454"
+            d="M10.331 7.162V2.756a.92.92 0 0 0-.919-.919H3.975a.394.394 0 1 1 0-.787h5.437c.943 0 1.706.764 1.706 1.706v4.406a.394.394 0 1 1-.787 0"
         />
     </svg>
 );
Index: src/icons/mcp/Ops.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import type { SVGProps } from \"react\";\nconst SvgOps = (props: SVGProps<SVGSVGElement>) => (\n    <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"1em\"\n        height=\"1em\"\n        fill=\"none\"\n        viewBox=\"0 0 20 20\"\n        {...props}\n    >\n        <path\n            fill=\"#000\"\n            d=\"M1.947 1.946c1.105-.682 2.58-.314 3.211.84a2.277 2.277 0 0 1-.843 3.2 2.53 2.53 0 0 1-1.841.21c-1.264 2.414-1.263 5.457.21 7.975 1.843 3.148 5.421 4.67 8.842 4.04.421-.052.79.158.895.63s-.158.892-.632.997c-4.052.734-8.316-1.05-10.474-4.827-1.789-3.096-1.683-6.769-.104-9.707-.053-.053-.053-.105-.106-.157a2.276 2.276 0 0 1 .842-3.2M8.211.162c4-.734 8.263 1.05 10.474 4.827 1.789 3.096 1.683 6.77.104 9.707.053.053.053.105.105.105a2.276 2.276 0 0 1-.841 3.2c-1.106.682-2.58.316-3.211-.839a2.277 2.277 0 0 1 .842-3.2 2.53 2.53 0 0 1 1.842-.21c1.263-2.414 1.263-5.457-.21-7.975-1.843-3.148-5.421-4.67-8.842-4.04a.82.82 0 0 1-.895-.578c-.105-.472.158-.892.632-.997m9.315 15.479c-.21-.367-.684-.473-1.052-.263-.369.21-.474.683-.263 1.05s.683.471 1.052.262c.368-.21.474-.682.263-1.05M9.736 4.085a.45.45 0 0 1 .527 0l2.369 1.467a.6.6 0 0 1 .263.507v1.974c0 .226.105.395.263.508l1.578.96a.6.6 0 0 1 .264.507v2.934a.6.6 0 0 1-.264.507l-2.368 1.466a.45.45 0 0 1-.526 0L10 13.787l-1.842 1.128a.45.45 0 0 1-.526 0l-2.37-1.466A.6.6 0 0 1 5 12.942v-2.934c0-.226.105-.395.263-.508l1.579-.959a.6.6 0 0 0 .263-.508V6.06c0-.226.106-.394.263-.507zm-3.157 6.6v1.635l1.315.79 1.317-.79v-1.635l-1.317-.79zm4.21 0v1.635l1.316.79 1.316-.79v-1.635l-1.316-.79zM8.685 6.736v1.636l1.315.79 1.315-.79V6.736L10 5.946zM3.789 3.521c-.21-.368-.684-.473-1.053-.263s-.473.683-.262 1.05.684.471 1.052.261c.369-.21.474-.681.263-1.048\"\n        />\n    </svg>\n);\nexport default SvgOps;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/Ops.tsx b/src/icons/mcp/Ops.tsx
--- a/src/icons/mcp/Ops.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/icons/mcp/Ops.tsx	(date 1755161373847)
@@ -5,13 +5,32 @@
         width="1em"
         height="1em"
         fill="none"
-        viewBox="0 0 20 20"
+        viewBox="0 0 26 26"
         {...props}
     >
-        <path
-            fill="#000"
-            d="M1.947 1.946c1.105-.682 2.58-.314 3.211.84a2.277 2.277 0 0 1-.843 3.2 2.53 2.53 0 0 1-1.841.21c-1.264 2.414-1.263 5.457.21 7.975 1.843 3.148 5.421 4.67 8.842 4.04.421-.052.79.158.895.63s-.158.892-.632.997c-4.052.734-8.316-1.05-10.474-4.827-1.789-3.096-1.683-6.769-.104-9.707-.053-.053-.053-.105-.106-.157a2.276 2.276 0 0 1 .842-3.2M8.211.162c4-.734 8.263 1.05 10.474 4.827 1.789 3.096 1.683 6.77.104 9.707.053.053.053.105.105.105a2.276 2.276 0 0 1-.841 3.2c-1.106.682-2.58.316-3.211-.839a2.277 2.277 0 0 1 .842-3.2 2.53 2.53 0 0 1 1.842-.21c1.263-2.414 1.263-5.457-.21-7.975-1.843-3.148-5.421-4.67-8.842-4.04a.82.82 0 0 1-.895-.578c-.105-.472.158-.892.632-.997m9.315 15.479c-.21-.367-.684-.473-1.052-.263-.369.21-.474.683-.263 1.05s.683.471 1.052.262c.368-.21.474-.682.263-1.05M9.736 4.085a.45.45 0 0 1 .527 0l2.369 1.467a.6.6 0 0 1 .263.507v1.974c0 .226.105.395.263.508l1.578.96a.6.6 0 0 1 .264.507v2.934a.6.6 0 0 1-.264.507l-2.368 1.466a.45.45 0 0 1-.526 0L10 13.787l-1.842 1.128a.45.45 0 0 1-.526 0l-2.37-1.466A.6.6 0 0 1 5 12.942v-2.934c0-.226.105-.395.263-.508l1.579-.959a.6.6 0 0 0 .263-.508V6.06c0-.226.106-.394.263-.507zm-3.157 6.6v1.635l1.315.79 1.317-.79v-1.635l-1.317-.79zm4.21 0v1.635l1.316.79 1.316-.79v-1.635l-1.316-.79zM8.685 6.736v1.636l1.315.79 1.315-.79V6.736L10 5.946zM3.789 3.521c-.21-.368-.684-.473-1.053-.263s-.473.683-.262 1.05.684.471 1.052.261c.369-.21.474-.681.263-1.048"
-        />
+        <g clipPath="url(#ops_svg__a)">
+            <path
+                fill="url(#ops_svg__b)"
+                d="M3.23 3.228c1.34-.828 3.129-.382 3.895 1.019a2.76 2.76 0 0 1-1.021 3.882 3.07 3.07 0 0 1-2.236.255c-1.532 2.929-1.532 6.621.256 9.677 2.235 3.82 6.578 5.665 10.729 4.901.51-.063.957.192 1.085.764.127.573-.192 1.083-.766 1.21-4.917.891-10.09-1.273-12.708-5.856C.293 15.324.42 10.867 2.336 7.302c-.064-.063-.064-.127-.128-.191a2.76 2.76 0 0 1 1.021-3.883m7.6-2.164A12.2 12.2 0 0 1 23.536 6.92c2.171 3.756 2.044 8.213.128 11.778.***************.127.127a2.76 2.76 0 0 1-1.022 3.883c-1.34.827-3.129.382-3.895-1.019a2.76 2.76 0 0 1 1.023-3.883 3.07 3.07 0 0 1 2.234-.255c1.532-2.928 1.533-6.62-.255-9.676-2.235-3.819-6.578-5.666-10.729-4.902a.994.994 0 0 1-1.085-.7c-.128-.573.191-1.082.766-1.209m11.302 18.78c-.256-.446-.83-.574-1.277-.32-.447.256-.575.829-.32 1.274.256.446.83.573 1.277.319.447-.255.575-.828.32-1.274M12.68 5.822a.55.55 0 0 1 .638 0l2.874 1.779c.192.137.32.343.32.616v2.396c0 .273.127.48.319.616l1.916 1.163a.73.73 0 0 1 .318.616v3.559c0 .273-.127.48-.318.616l-2.874 1.78a.55.55 0 0 1-.639 0L13 17.594l-2.234 1.368a.55.55 0 0 1-.639 0l-2.874-1.779a.73.73 0 0 1-.32-.616v-3.559c0-.274.128-.479.32-.616l1.916-1.163a.73.73 0 0 0 .32-.616V8.218c0-.274.127-.48.319-.616zM8.85 13.83v1.984l1.596.959 1.596-.96v-1.983l-1.596-.958zm5.108 0v1.984l1.597.959 1.596-.96v-1.983l-1.596-.958zM11.404 9.04v1.984l1.596.959 1.597-.96V9.04L13 8.082zm-5.94-3.902c-.255-.445-.83-.573-1.277-.318s-.574.827-.319 1.272c.256.446.83.573 1.278.319.446-.255.574-.827.319-1.273"
+            />
+        </g>
+        <defs>
+            <linearGradient
+                id="ops_svg__b"
+                x1={2.081}
+                x2={25.133}
+                y1={4.507}
+                y2={20.28}
+                gradientUnits="userSpaceOnUse"
+            >
+                <stop stopColor="#6DA6FF" />
+                <stop offset={0.5} stopColor="#BBD4FF" />
+                <stop offset={1} stopColor="#7E7DFF" />
+            </linearGradient>
+            <clipPath id="ops_svg__a">
+                <path fill="#fff" d="M0 0h26v26H0z" />
+            </clipPath>
+        </defs>
     </svg>
 );
 export default SvgOps;
Index: src/comatestack/MCP/MCPSquireDetail/MCPInfo.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Flex, Space, Typography, Divider} from 'antd';\nimport styled from '@emotion/styled';\nimport {useCallback} from 'react';\nimport MCPServerAvatar from '@/components/MCP/MCPServerAvatar';\nimport {useMCPServerId} from '@/components/MCP/hooks';\nimport {loadMCPServer, useMCPServer} from '@/regions/mcp/mcpServer';\nimport {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';\nimport MCPServerProtocolTypeTag from '@/components/MCP/MCPServerProtocolTypeTag';\nimport MCPServerTypeTag from '@/components/MCP/MCPServerTypeTag';\nimport TagGroup from '@/components/MCP/TagGroup';\nimport {IconEye} from '@/icons/mcp';\nimport {MCPCollectButton} from '@/components/MCP/MCPCollectButton';\nimport DescriptionItem from '@/design/MCP/MCPDescriptionItem';\nimport {UserAvatarList} from '@/components/MCP/UserAvatarList';\n\nconst DepartmentText = styled.div`\n    margin: 4px 0 14px;\n    color: #8F8F8F;\n`;\n\nconst MCPInfo = () => {\n    const mcpServerId = useMCPServerId();\n    const mcpServer = useMCPServer(mcpServerId);\n\n    const refresh = useCallback(\n        () => {\n            loadMCPServer({mcpServerId});\n        },\n        [mcpServerId]\n    );\n\n    return (\n        <Flex vertical gap={16}>\n            <Flex gap={14}>\n                <MCPServerAvatar size={88} icon={mcpServer?.icon} />\n                <Flex vertical justify=\"space-between\" style={{flex: 1}}>\n                    <Flex justify=\"space-between\">\n                        <Typography.Title level={3}>{mcpServer?.name}</Typography.Title>\n                        <Space split={<Divider type=\"vertical\" style={{borderColor: '#D9D9D9'}} />}>\n                            <Flex align=\"center\" gap={4}>\n                                <IconEye style={{marginRight: 4}} />\n                                {mcpServer?.viewCount ?? 0}\n                            </Flex>\n                            <MCPCollectButton\n                                refresh={refresh}\n                                favorite={mcpServer?.favorite}\n                                serverId={mcpServerId}\n                            />\n                            <MCPSubscribeButton\n                                id={mcpServerId}\n                                workspaceId={mcpServer?.workspaceId}\n                                showText\n                            />\n                        </Space>\n                    </Flex>\n                    <DepartmentText>{mcpServer?.departmentName || '暂无部门信息'}</DepartmentText>\n                    <Flex align=\"center\" gap={40}>\n                        <DescriptionItem label=\"类型\">\n                            <MCPServerTypeTag type={mcpServer?.serverSourceType} />\n                        </DescriptionItem>\n                        <DescriptionItem label=\"协议\">\n                            <MCPServerProtocolTypeTag type={mcpServer?.serverProtocolType} />\n                        </DescriptionItem>\n                        <DescriptionItem label=\"场景\">\n                            <TagGroup\n                                labels={(mcpServer?.labels || []).map(\n                                    label => ({id: label.id, label: label.labelValue})\n                                )}\n                                color=\"light-purple\"\n                                maxNum={3}\n                                prefix={null}\n                            />\n                        </DescriptionItem>\n                        <DescriptionItem label=\"联系人\">\n                            <UserAvatarList users={mcpServer?.contacts ?? []} max={2} />\n                        </DescriptionItem>\n                    </Flex>\n                </Flex>\n            </Flex>\n            <Typography.Paragraph type=\"secondary\">\n                {mcpServer?.description || '暂无描述'}\n            </Typography.Paragraph>\n        </Flex>\n    );\n};\n\nexport default MCPInfo;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSquireDetail/MCPInfo.tsx b/src/comatestack/MCP/MCPSquireDetail/MCPInfo.tsx
--- a/src/comatestack/MCP/MCPSquireDetail/MCPInfo.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/comatestack/MCP/MCPSquireDetail/MCPInfo.tsx	(date 1755169984637)
@@ -39,7 +39,7 @@
                         <Space split={<Divider type="vertical" style={{borderColor: '#D9D9D9'}} />}>
                             <Flex align="center" gap={4}>
                                 <IconEye style={{marginRight: 4}} />
-                                {mcpServer?.viewCount ?? 0}
+                                {mcpServer?.serverMetrics?.viewCount ?? 0}
                             </Flex>
                             <MCPCollectButton
                                 refresh={refresh}
@@ -56,7 +56,7 @@
                     <DepartmentText>{mcpServer?.departmentName || '暂无部门信息'}</DepartmentText>
                     <Flex align="center" gap={40}>
                         <DescriptionItem label="类型">
-                            <MCPServerTypeTag type={mcpServer?.serverSourceType} />
+                            <MCPServerTypeTag type={mcpServer?.serverProtocolType} />
                         </DescriptionItem>
                         <DescriptionItem label="协议">
                             <MCPServerProtocolTypeTag type={mcpServer?.serverProtocolType} />
Index: src/icons/mcp/Test.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import type { SVGProps } from \"react\";\nconst SvgTest = (props: SVGProps<SVGSVGElement>) => (\n    <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"1em\"\n        height=\"1em\"\n        fill=\"none\"\n        viewBox=\"0 0 19 20\"\n        {...props}\n    >\n        <path\n            fill=\"#000\"\n            d=\"M8.25 16.097a6.4 6.4 0 0 0 1.834 1.954v.002h.001a6.4 6.4 0 0 0 2.191.975c-.921.62-1.98.972-3.109.972-2.257 0-4.241-1.41-5.385-3.544-.795.007-1.263.176-1.474.441a2.2 2.2 0 0 0-.475 1.303.913.913 0 0 1-.962.867.92.92 0 0 1-.87-.96 4.03 4.03 0 0 1 .87-2.345c.494-.624 1.241-.976 2.202-1.092q-.205-.75-.279-1.527H.918a.92.92 0 0 1-.847-.564.912.912 0 0 1 .847-1.264h1.877a9 9 0 0 1 .357-1.8c-.819-.148-1.463-.489-1.906-1.046a4 4 0 0 1-.87-2.345.913.913 0 0 1 .869-.96.92.92 0 0 1 .962.867c.026.509.184.934.475 1.302.188.237.582.397 1.231.434a7.3 7.3 0 0 1 1.638-1.959 4.105 4.105 0 0 1 1.85-5.34 4.133 4.133 0 0 1 5.45 1.538 4.1 4.1 0 0 1 .133 3.976q.225.203.433.425c-.756.038-1.5.21-2.196.509a3.9 3.9 0 0 0-1.137-.51v1.14A6.4 6.4 0 0 0 8.25 9.5V6.405c-2.091.55-3.666 2.949-3.666 5.824s1.575 5.273 3.666 5.824zm7.683-9.318q.134-.341.154-.748a.914.914 0 0 1 .963-.867.92.92 0 0 1 .869.96 4.1 4.1 0 0 1-.364 1.522 6.4 6.4 0 0 0-1.622-.867M7.141 4.853a5.4 5.4 0 0 1 4.3.107 2.28 2.28 0 0 0-1.02-2.843 2.296 2.296 0 0 0-2.957.647 2.28 2.28 0 0 0-.323 2.09zM17.292 15.82l1.522 1.943a.913.913 0 0 1-.836 1.46.92.92 0 0 1-.609-.335l-1.44-1.837a4.593 4.593 0 0 1-6.021-1.227 4.563 4.563 0 0 1 .617-6.1 4.59 4.59 0 0 1 6.146-.002 4.563 4.563 0 0 1 .622 6.099m-4.029.017a2.76 2.76 0 0 0 2.752-1.363 2.737 2.737 0 0 0-1.054-3.753 2.76 2.76 0 0 0-3.04.244 2.741 2.741 0 0 0 1.342 4.872\"\n        />\n    </svg>\n);\nexport default SvgTest;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/Test.tsx b/src/icons/mcp/Test.tsx
--- a/src/icons/mcp/Test.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/icons/mcp/Test.tsx	(date 1755161373848)
@@ -5,13 +5,27 @@
         width="1em"
         height="1em"
         fill="none"
-        viewBox="0 0 19 20"
+        viewBox="0 0 26 26"
         {...props}
     >
         <path
-            fill="#000"
-            d="M8.25 16.097a6.4 6.4 0 0 0 1.834 1.954v.002h.001a6.4 6.4 0 0 0 2.191.975c-.921.62-1.98.972-3.109.972-2.257 0-4.241-1.41-5.385-3.544-.795.007-1.263.176-1.474.441a2.2 2.2 0 0 0-.475 1.303.913.913 0 0 1-.962.867.92.92 0 0 1-.87-.96 4.03 4.03 0 0 1 .87-2.345c.494-.624 1.241-.976 2.202-1.092q-.205-.75-.279-1.527H.918a.92.92 0 0 1-.847-.564.912.912 0 0 1 .847-1.264h1.877a9 9 0 0 1 .357-1.8c-.819-.148-1.463-.489-1.906-1.046a4 4 0 0 1-.87-2.345.913.913 0 0 1 .869-.96.92.92 0 0 1 .962.867c.026.509.184.934.475 1.302.188.237.582.397 1.231.434a7.3 7.3 0 0 1 1.638-1.959 4.105 4.105 0 0 1 1.85-5.34 4.133 4.133 0 0 1 5.45 1.538 4.1 4.1 0 0 1 .133 3.976q.225.203.433.425c-.756.038-1.5.21-2.196.509a3.9 3.9 0 0 0-1.137-.51v1.14A6.4 6.4 0 0 0 8.25 9.5V6.405c-2.091.55-3.666 2.949-3.666 5.824s1.575 5.273 3.666 5.824zm7.683-9.318q.134-.341.154-.748a.914.914 0 0 1 .963-.867.92.92 0 0 1 .869.96 4.1 4.1 0 0 1-.364 1.522 6.4 6.4 0 0 0-1.622-.867M7.141 4.853a5.4 5.4 0 0 1 4.3.107 2.28 2.28 0 0 0-1.02-2.843 2.296 2.296 0 0 0-2.957.647 2.28 2.28 0 0 0-.323 2.09zM17.292 15.82l1.522 1.943a.913.913 0 0 1-.836 1.46.92.92 0 0 1-.609-.335l-1.44-1.837a4.593 4.593 0 0 1-6.021-1.227 4.563 4.563 0 0 1 .617-6.1 4.59 4.59 0 0 1 6.146-.002 4.563 4.563 0 0 1 .622 6.099m-4.029.017a2.76 2.76 0 0 0 2.752-1.363 2.737 2.737 0 0 0-1.054-3.753 2.76 2.76 0 0 0-3.04.244 2.741 2.741 0 0 0 1.342 4.872"
+            fill="url(#test_svg__a)"
+            d="M11.027 20.528a7.9 7.9 0 0 0 2.258 2.413v.002h.002a7.9 7.9 0 0 0 2.698 1.204c-1.134.765-2.44 1.2-3.829 1.2-2.78 0-5.223-1.741-6.632-4.376-.979.009-1.555.218-1.815.545-.36.455-.553.98-.585 1.609a1.13 1.13 0 1 1-2.256-.115c.056-1.092.414-2.067 1.07-2.896.61-.77 1.53-1.204 2.712-1.348a11.3 11.3 0 0 1-.343-1.885h-2.31a1.128 1.128 0 1 1 0-2.258h2.311c.072-.753.22-1.497.44-2.221-1.009-.183-1.802-.604-2.347-1.293-.657-.828-1.016-1.804-1.072-2.895a1.13 1.13 0 0 1 2.256-.115c.031.629.226 1.153.585 1.608.23.293.716.49 1.516.535a9 9 0 0 1 2.017-2.418 5.08 5.08 0 1 1 9.155.215q.275.25.532.525a7.9 7.9 0 0 0-2.704.627 4.8 4.8 0 0 0-1.401-.63v1.41a7.9 7.9 0 0 0-2.258 2.412V8.562c-2.576.68-4.515 3.64-4.515 7.19s1.94 6.511 4.515 7.191zM20.49 9.023a3 3 0 0 0 .19-.924 1.13 1.13 0 0 1 2.255.115 5.1 5.1 0 0 1-.447 1.88 7.9 7.9 0 0 0-1.998-1.07M9.661 6.645a6.64 6.64 0 0 1 5.295.132 2.823 2.823 0 1 0-5.295-.131zm12.502 13.541 1.874 2.4a1.129 1.129 0 0 1-1.779 1.389l-1.773-2.27a5.644 5.644 0 1 1 1.678-1.517zm-4.962.02a3.386 3.386 0 1 0 .892-6.712 3.386 3.386 0 0 0-.892 6.713"
         />
+        <defs>
+            <linearGradient
+                id="test_svg__a"
+                x1={5.249}
+                x2={24.215}
+                y1={4.419}
+                y2={24.609}
+                gradientUnits="userSpaceOnUse"
+            >
+                <stop stopColor="#A292FF" />
+                <stop offset={0.452} stopColor="#FBCAFF" />
+                <stop offset={1} stopColor="#DB57FF" />
+            </linearGradient>
+        </defs>
     </svg>
 );
 export default SvgTest;
Index: src/comatestack/MCP/MCPSquare/RegionNavigation/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>/* eslint-disable max-lines */\nimport styled from '@emotion/styled';\nimport {Flex, Typography} from 'antd';\nimport {ReactNode} from 'react';\nimport {IconArrowRight, IconCode, IconOps, IconTest} from '@/icons/mcp';\nimport {MCPZoneLink} from '@/links/mcp';\n\nconst NavigationContainer = styled(Flex)`\n    margin-top: 21px;\n    gap: 20px;\n`;\n\nconst RegionCard = styled.div<{ isActive?: boolean, disabled?: boolean, regionType?: string }>`\n    flex: 1;\n    padding: 24px 20px;\n    border: 1px solid #D9D9D9;\n    border-radius: 10px;\n    background: ${props => {\n        switch (props.regionType) {\n            case 'dev':\n                return 'linear-gradient(329.12deg, #E7F1FF -8.74%, #FFFFFF 92.24%)';\n            case 'ops':\n                return 'linear-gradient(328.39deg, #DEF3FF -8.86%, #FFFFFF 89.14%)';\n            case 'test':\n                return 'linear-gradient(328.02deg, #F0ECFE -8.92%, #FFFFFF 92.24%)';\n            default:\n                return 'linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%)';\n        }\n    }};\n    transition: all 0.3s ease;\n    position: relative;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n\n    &:hover {\n        ${props =>\n        !props.disabled\n            && `\n            border-color: #0083FF;\n            transform: translateY(-2px);\n            box-shadow: 0px 5px 16px 0px #00000021;\n        `}\n    }\n`;\n\nconst CardHeader = styled(Flex)`\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 12px;\n`;\n\nconst IconWrapper = styled.div`\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-right: 12px;\n    svg {\n        font-size: 20px;\n    }\n`;\n\nconst CardTitle = styled(Typography.Title)`\n    margin: 0 !important;\n    font-size: 16px !important;\n    font-weight: 600 !important;\n    line-height: 28px !important;\n`;\n\nconst CardDescription = styled(Typography.Text)`\n    font-size: 12px;\n    line-height: 20px;\n    color: #5C5C5C;\n`;\n\nconst HoverActionContainer = styled(Flex)`\n    align-items: center;\n    gap: 8px;\n`;\n\nconst HoverText = styled.span`\n    color: #0083FF;\n    font-size: 12px;\n    line-height: 20px;\n    opacity: 0;\n    transition: opacity 0.3s ease;\n    white-space: nowrap;\n`;\n\nconst ArrowIcon = styled(IconArrowRight)`\n    color: #8c8c8c;\n    transition: all 0.3s ease;\n    font-size: 16px;\n\n    svg {\n        transition: all 0.3s ease;\n    }\n\n    path {\n        fill: #8c8c8c !important;\n        transition: fill 0.3s ease;\n    }\n`;\n\nconst StyledRegionCard = styled(RegionCard)`\n    &:hover {\n        ${HoverText} {\n            opacity: 1;\n        }\n\n        ${ArrowIcon} {\n            color: #0083FF;\n            path {\n                fill: #0083FF !important;\n            }\n        }\n    }\n`;\n\ninterface RegionItem {\n    key: string;\n    title: string;\n    description: string;\n    icon: ReactNode;\n    status?: 'active' | 'coming';\n    disabled?: boolean;\n    onClick?: () => void;\n    // 用id来跳转\n    id: string;\n}\n\ninterface Props {\n    activeRegion?: string;\n    onRegionChange?: (region: string) => void;\n}\n\nconst RegionNavigation = ({activeRegion = 'dev', onRegionChange}: Props) => {\n    const regions: RegionItem[] = [\n        {\n            key: 'dev',\n            title: '开发专区',\n            description:\n                '面向运维的MCP中心，汇聚各类高质量工具，在通用场景、云原生、业务运维等各领域助力提升效率，分析根因，提升排障效率。',\n            icon: <IconCode />,\n            status: 'active',\n            disabled: false,\n            id: '1',\n        },\n        {\n            key: 'ops',\n            title: '运维专区',\n            description:\n                '面向运维的MCP中心，汇聚各类高质量工具，在通用场景、云原生、业务运维等各领域助力提升效率，分析根因，提升排障效率。',\n            icon: <IconOps />,\n            status: 'coming',\n            disabled: false,\n            id: '2',\n        },\n        {\n            key: 'test',\n            title: '测试专区（敬请期待）',\n            description:\n                '面向运维的MCP中心，汇聚各类高质量工具，在通用场景、云原生、业务运维等各领域助力提升效率，分析根因，提升排障效率。',\n            icon: <IconTest />,\n            status: 'coming',\n            disabled: false,\n            id: '3',\n        },\n    ];\n\n    const handleRegionClick = (region: RegionItem) => {\n        if (!region.disabled && region.status === 'active') {\n            onRegionChange?.(region.key);\n            window.open(MCPZoneLink.toUrl({zoneId: region.id}), '_blank');\n        }\n    };\n\n    return (\n        <NavigationContainer>\n            {regions.map(region => (\n                <StyledRegionCard\n                    key={region.key}\n                    isActive={activeRegion === region.key}\n                    disabled={region.disabled}\n                    regionType={region.key}\n                    onClick={() => handleRegionClick(region)}\n                >\n                    <CardHeader>\n                        <Flex>\n                            <IconWrapper>{region.icon}</IconWrapper>\n                            <CardTitle level={4}>{region.title}</CardTitle>\n                        </Flex>\n                        <HoverActionContainer>\n                            <HoverText>进入专区</HoverText>\n                            <ArrowIcon />\n                        </HoverActionContainer>\n                    </CardHeader>\n                    <CardDescription>{region.description}</CardDescription>\n                </StyledRegionCard>\n            ))}\n        </NavigationContainer>\n    );\n};\n\nexport default RegionNavigation;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSquare/RegionNavigation/index.tsx b/src/comatestack/MCP/MCPSquare/RegionNavigation/index.tsx
--- a/src/comatestack/MCP/MCPSquare/RegionNavigation/index.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/comatestack/MCP/MCPSquare/RegionNavigation/index.tsx	(date 1755157814439)
@@ -2,7 +2,7 @@
 import styled from '@emotion/styled';
 import {Flex, Typography} from 'antd';
 import {ReactNode} from 'react';
-import {IconArrowRight, IconCode, IconOps, IconTest} from '@/icons/mcp';
+import {IconArrowRight, IconDev, IconOps, IconTest} from '@/icons/mcp';
 import {MCPZoneLink} from '@/links/mcp';
 
 const NavigationContainer = styled(Flex)`
@@ -139,7 +139,7 @@
             title: '开发专区',
             description:
                 '面向运维的MCP中心，汇聚各类高质量工具，在通用场景、云原生、业务运维等各领域助力提升效率，分析根因，提升排障效率。',
-            icon: <IconCode />,
+            icon: <IconDev />,
             status: 'active',
             disabled: false,
             id: '1',
Index: src/icons/mcp/test.svg
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><svg width=\"19\" height=\"20\" viewBox=\"0 0 19 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<path d=\"M8.2504 16.0971C8.71741 16.8693 9.34198 17.535 10.0836 18.0509V18.0527H10.0854C10.7371 18.5052 11.4786 18.8417 12.276 19.0282C11.3549 19.6471 10.2953 20 9.16698 20C6.91036 20 4.92597 18.5893 3.78208 16.4555C2.98741 16.4628 2.51903 16.632 2.30822 16.8971C2.01583 17.2655 1.8591 17.6907 1.83343 18.1999C1.82729 18.3199 1.79752 18.4375 1.74581 18.546C1.6941 18.6545 1.62146 18.7518 1.53205 18.8324C1.44264 18.9129 1.33821 18.975 1.22471 19.0153C1.11121 19.0556 0.990873 19.0732 0.870567 19.067C0.75026 19.0609 0.632341 19.0312 0.523541 18.9796C0.414741 18.928 0.317192 18.8556 0.236463 18.7664C0.155734 18.6772 0.0934066 18.5731 0.0530387 18.4599C0.0126707 18.3467 -0.00494683 18.2266 0.00119188 18.1066C0.0470208 17.2226 0.337576 16.4327 0.870108 15.7616C1.36506 15.1381 2.11207 14.7861 3.07265 14.67C2.93666 14.1699 2.84344 13.6591 2.79401 13.1433H0.917771C0.797392 13.1433 0.67819 13.1196 0.56697 13.0737C0.45575 13.0278 0.354691 12.9604 0.269565 12.8755C0.184438 12.7906 0.116912 12.6899 0.0708408 12.5789C0.02477 12.468 0.00105753 12.3491 0.00105753 12.229C0.00105753 12.109 0.02477 11.9901 0.0708408 11.8791C0.116912 11.7682 0.184438 11.6674 0.269565 11.5825C0.354691 11.4976 0.45575 11.4303 0.56697 11.3844C0.67819 11.3384 0.797392 11.3148 0.917771 11.3148H2.79492C2.85316 10.7046 2.97292 10.1018 3.15239 9.51559C2.33297 9.36749 1.68861 9.02648 1.24591 8.4688C0.712457 7.79775 0.420985 7.00786 0.376073 6.1238C0.363675 5.88157 0.448256 5.64435 0.61121 5.46433C0.774163 5.2843 1.00214 5.17622 1.24499 5.16385C1.48784 5.15149 1.72566 5.23585 1.90615 5.39839C2.08664 5.56092 2.195 5.78832 2.2074 6.03054C2.23306 6.53977 2.39071 6.96489 2.68218 7.33332C2.87008 7.57011 3.26421 7.7301 3.91315 7.76667C4.33905 7.02266 4.89357 6.35967 5.55108 5.80839C5.26379 5.17435 5.14241 4.478 5.19829 3.78446C5.25417 3.09092 5.48549 2.42284 5.87061 1.84271C6.25573 1.26258 6.78207 0.789355 7.40041 0.467286C8.01874 0.145219 8.70887 -0.0151674 9.40624 0.00112877C10.1036 0.0174249 10.7855 0.209871 11.388 0.560468C11.9905 0.911065 12.4941 1.40836 12.8515 2.00584C13.209 2.60332 13.4087 3.28147 13.4319 3.97686C13.4552 4.67226 13.3013 5.36217 12.9845 5.98209C13.1339 6.1174 13.2778 6.2591 13.4172 6.40721C12.6607 6.44533 11.917 6.61746 11.221 6.91552C10.8717 6.68574 10.4877 6.51349 10.0836 6.40538V7.54634C9.34198 8.06223 8.71741 8.72786 8.2504 9.50005V6.40538C6.15877 6.95575 4.58408 9.35378 4.58408 12.229C4.58408 15.1043 6.15877 17.5023 8.2504 18.0527V16.0962V16.0971ZM15.9332 6.7793C16.0221 6.55074 16.0734 6.30298 16.0871 6.03054C16.0995 5.78832 16.2079 5.56092 16.3884 5.39839C16.5689 5.23585 16.8067 5.15149 17.0496 5.16385C17.2924 5.17622 17.5204 5.2843 17.6833 5.46433C17.8463 5.64435 17.9309 5.88157 17.9185 6.1238C17.8953 6.64956 17.7722 7.16614 17.5555 7.64599C17.0594 7.28065 16.513 6.98873 15.9332 6.7793ZM7.14134 4.85301C7.82687 4.57561 8.56231 4.44194 9.30188 4.46034C10.0414 4.47875 10.7693 4.64882 11.4401 4.95998C11.5763 4.61774 11.6276 4.24774 11.5896 3.88147C11.5516 3.5152 11.4254 3.16352 11.2219 2.85634C11.0183 2.54917 10.7433 2.2956 10.4204 2.11722C10.0974 1.93884 9.73602 1.84093 9.36697 1.83183C8.99792 1.82272 8.63212 1.90269 8.30072 2.06493C7.96931 2.22716 7.68211 2.46686 7.46356 2.76362C7.24501 3.06039 7.10158 3.40542 7.04548 3.76937C6.98938 4.13331 7.02226 4.50539 7.14134 4.85393V4.85301ZM17.2924 15.8201L18.814 17.7629C18.9594 17.9543 19.0234 18.1951 18.9923 18.4332C18.9612 18.6713 18.8374 18.8877 18.6476 19.0355C18.4579 19.1833 18.2175 19.2507 17.9783 19.2232C17.7391 19.1957 17.5204 19.0754 17.3694 18.8883L15.9295 17.0507C14.9517 17.6266 13.7945 17.82 12.6819 17.5933C11.5694 17.3666 10.5808 16.736 9.90787 15.8238C9.23489 14.9115 8.92542 13.7825 9.03942 12.6557C9.15342 11.5288 9.68278 10.4843 10.5249 9.72449C11.367 8.96474 12.462 8.54386 13.5975 8.54345C14.733 8.54303 15.8282 8.9631 16.6709 9.72223C17.5136 10.4814 18.0437 11.5255 18.1586 12.6523C18.2734 13.7791 17.9647 14.9083 17.2924 15.821V15.8201ZM13.2632 15.8366C13.6234 15.8844 13.9896 15.8604 14.3405 15.7662C14.6914 15.6719 15.0201 15.5092 15.3076 15.2875C15.5951 15.0657 15.8357 14.7893 16.0154 14.4743C16.1952 14.1592 16.3105 13.8118 16.3548 13.452C16.3991 13.0922 16.3714 12.7272 16.2734 12.3782C16.1754 12.0291 16.009 11.7029 15.7838 11.4184C15.5586 11.1339 15.2791 10.8967 14.9615 10.7206C14.6438 10.5445 14.2943 10.4329 13.9332 10.3923C13.2132 10.3115 12.4904 10.5173 11.9217 10.9651C11.3531 11.4129 10.9845 12.0664 10.8963 12.7836C10.808 13.5009 11.0071 14.2239 11.4503 14.7956C11.8935 15.3673 12.545 15.7414 13.2632 15.8366Z\" fill=\"black\"/>\n</svg>\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/test.svg b/src/icons/mcp/test.svg
--- a/src/icons/mcp/test.svg	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/icons/mcp/test.svg	(date 1755156387601)
@@ -1,3 +1,10 @@
-<svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
-<path d="M8.2504 16.0971C8.71741 16.8693 9.34198 17.535 10.0836 18.0509V18.0527H10.0854C10.7371 18.5052 11.4786 18.8417 12.276 19.0282C11.3549 19.6471 10.2953 20 9.16698 20C6.91036 20 4.92597 18.5893 3.78208 16.4555C2.98741 16.4628 2.51903 16.632 2.30822 16.8971C2.01583 17.2655 1.8591 17.6907 1.83343 18.1999C1.82729 18.3199 1.79752 18.4375 1.74581 18.546C1.6941 18.6545 1.62146 18.7518 1.53205 18.8324C1.44264 18.9129 1.33821 18.975 1.22471 19.0153C1.11121 19.0556 0.990873 19.0732 0.870567 19.067C0.75026 19.0609 0.632341 19.0312 0.523541 18.9796C0.414741 18.928 0.317192 18.8556 0.236463 18.7664C0.155734 18.6772 0.0934066 18.5731 0.0530387 18.4599C0.0126707 18.3467 -0.00494683 18.2266 0.00119188 18.1066C0.0470208 17.2226 0.337576 16.4327 0.870108 15.7616C1.36506 15.1381 2.11207 14.7861 3.07265 14.67C2.93666 14.1699 2.84344 13.6591 2.79401 13.1433H0.917771C0.797392 13.1433 0.67819 13.1196 0.56697 13.0737C0.45575 13.0278 0.354691 12.9604 0.269565 12.8755C0.184438 12.7906 0.116912 12.6899 0.0708408 12.5789C0.02477 12.468 0.00105753 12.3491 0.00105753 12.229C0.00105753 12.109 0.02477 11.9901 0.0708408 11.8791C0.116912 11.7682 0.184438 11.6674 0.269565 11.5825C0.354691 11.4976 0.45575 11.4303 0.56697 11.3844C0.67819 11.3384 0.797392 11.3148 0.917771 11.3148H2.79492C2.85316 10.7046 2.97292 10.1018 3.15239 9.51559C2.33297 9.36749 1.68861 9.02648 1.24591 8.4688C0.712457 7.79775 0.420985 7.00786 0.376073 6.1238C0.363675 5.88157 0.448256 5.64435 0.61121 5.46433C0.774163 5.2843 1.00214 5.17622 1.24499 5.16385C1.48784 5.15149 1.72566 5.23585 1.90615 5.39839C2.08664 5.56092 2.195 5.78832 2.2074 6.03054C2.23306 6.53977 2.39071 6.96489 2.68218 7.33332C2.87008 7.57011 3.26421 7.7301 3.91315 7.76667C4.33905 7.02266 4.89357 6.35967 5.55108 5.80839C5.26379 5.17435 5.14241 4.478 5.19829 3.78446C5.25417 3.09092 5.48549 2.42284 5.87061 1.84271C6.25573 1.26258 6.78207 0.789355 7.40041 0.467286C8.01874 0.145219 8.70887 -0.0151674 9.40624 0.00112877C10.1036 0.0174249 10.7855 0.209871 11.388 0.560468C11.9905 0.911065 12.4941 1.40836 12.8515 2.00584C13.209 2.60332 13.4087 3.28147 13.4319 3.97686C13.4552 4.67226 13.3013 5.36217 12.9845 5.98209C13.1339 6.1174 13.2778 6.2591 13.4172 6.40721C12.6607 6.44533 11.917 6.61746 11.221 6.91552C10.8717 6.68574 10.4877 6.51349 10.0836 6.40538V7.54634C9.34198 8.06223 8.71741 8.72786 8.2504 9.50005V6.40538C6.15877 6.95575 4.58408 9.35378 4.58408 12.229C4.58408 15.1043 6.15877 17.5023 8.2504 18.0527V16.0962V16.0971ZM15.9332 6.7793C16.0221 6.55074 16.0734 6.30298 16.0871 6.03054C16.0995 5.78832 16.2079 5.56092 16.3884 5.39839C16.5689 5.23585 16.8067 5.15149 17.0496 5.16385C17.2924 5.17622 17.5204 5.2843 17.6833 5.46433C17.8463 5.64435 17.9309 5.88157 17.9185 6.1238C17.8953 6.64956 17.7722 7.16614 17.5555 7.64599C17.0594 7.28065 16.513 6.98873 15.9332 6.7793ZM7.14134 4.85301C7.82687 4.57561 8.56231 4.44194 9.30188 4.46034C10.0414 4.47875 10.7693 4.64882 11.4401 4.95998C11.5763 4.61774 11.6276 4.24774 11.5896 3.88147C11.5516 3.5152 11.4254 3.16352 11.2219 2.85634C11.0183 2.54917 10.7433 2.2956 10.4204 2.11722C10.0974 1.93884 9.73602 1.84093 9.36697 1.83183C8.99792 1.82272 8.63212 1.90269 8.30072 2.06493C7.96931 2.22716 7.68211 2.46686 7.46356 2.76362C7.24501 3.06039 7.10158 3.40542 7.04548 3.76937C6.98938 4.13331 7.02226 4.50539 7.14134 4.85393V4.85301ZM17.2924 15.8201L18.814 17.7629C18.9594 17.9543 19.0234 18.1951 18.9923 18.4332C18.9612 18.6713 18.8374 18.8877 18.6476 19.0355C18.4579 19.1833 18.2175 19.2507 17.9783 19.2232C17.7391 19.1957 17.5204 19.0754 17.3694 18.8883L15.9295 17.0507C14.9517 17.6266 13.7945 17.82 12.6819 17.5933C11.5694 17.3666 10.5808 16.736 9.90787 15.8238C9.23489 14.9115 8.92542 13.7825 9.03942 12.6557C9.15342 11.5288 9.68278 10.4843 10.5249 9.72449C11.367 8.96474 12.462 8.54386 13.5975 8.54345C14.733 8.54303 15.8282 8.9631 16.6709 9.72223C17.5136 10.4814 18.0437 11.5255 18.1586 12.6523C18.2734 13.7791 17.9647 14.9083 17.2924 15.821V15.8201ZM13.2632 15.8366C13.6234 15.8844 13.9896 15.8604 14.3405 15.7662C14.6914 15.6719 15.0201 15.5092 15.3076 15.2875C15.5951 15.0657 15.8357 14.7893 16.0154 14.4743C16.1952 14.1592 16.3105 13.8118 16.3548 13.452C16.3991 13.0922 16.3714 12.7272 16.2734 12.3782C16.1754 12.0291 16.009 11.7029 15.7838 11.4184C15.5586 11.1339 15.2791 10.8967 14.9615 10.7206C14.6438 10.5445 14.2943 10.4329 13.9332 10.3923C13.2132 10.3115 12.4904 10.5173 11.9217 10.9651C11.3531 11.4129 10.9845 12.0664 10.8963 12.7836C10.808 13.5009 11.0071 14.2239 11.4503 14.7956C11.8935 15.3673 12.545 15.7414 13.2632 15.8366Z" fill="black"/>
+<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
+<path d="M11.0272 20.5284C11.6024 21.4819 12.3716 22.3037 13.2849 22.9407V22.943H13.2872C14.0898 23.5018 15.003 23.9172 15.9851 24.1475C14.8506 24.9117 13.5457 25.3474 12.1561 25.3474C9.37687 25.3474 6.93293 23.6056 5.52414 20.9709C4.54544 20.9799 3.9686 21.1888 3.70897 21.5161C3.34887 21.971 3.15584 22.496 3.12423 23.1247C3.11667 23.2729 3.08 23.4181 3.01631 23.5521C2.95263 23.6861 2.86317 23.8062 2.75306 23.9057C2.64294 24.0051 2.51432 24.0819 2.37454 24.1316C2.23476 24.1813 2.08655 24.203 1.93838 24.1954C1.79022 24.1879 1.64499 24.1512 1.51099 24.0875C1.377 24.0238 1.25686 23.9344 1.15743 23.8243C1.05801 23.7141 0.981249 23.5855 0.931532 23.4457C0.881816 23.306 0.860119 23.1577 0.867679 23.0096C0.924121 21.918 1.28196 20.9427 1.93782 20.1141C2.54739 19.3442 3.4674 18.9096 4.65042 18.7663C4.48293 18.1487 4.36813 17.5181 4.30725 16.8811H1.99652C1.84826 16.8811 1.70146 16.852 1.56448 16.7952C1.4275 16.7385 1.30304 16.6554 1.1982 16.5505C1.09336 16.4457 1.0102 16.3213 0.953457 16.1843C0.896717 16.0473 0.867513 15.9005 0.867513 15.7523C0.867513 15.604 0.896717 15.4572 0.953457 15.3202C1.0102 15.1833 1.09336 15.0588 1.1982 14.954C1.30304 14.8492 1.4275 14.766 1.56448 14.7093C1.70146 14.6526 1.84826 14.6234 1.99652 14.6234H4.30838C4.3801 13.87 4.52759 13.1257 4.74863 12.4019C3.73945 12.219 2.94587 11.798 2.40064 11.1094C1.74366 10.2808 1.38469 9.30548 1.32937 8.21389C1.31411 7.9148 1.41827 7.6219 1.61896 7.39961C1.81965 7.17733 2.10043 7.04388 2.39951 7.02861C2.6986 7.01334 2.9915 7.11751 3.21379 7.3182C3.43607 7.51889 3.56953 7.79966 3.58479 8.09875C3.6164 8.72751 3.81056 9.25242 4.16953 9.70734C4.40094 9.99971 4.88635 10.1973 5.68556 10.2424C6.21009 9.32375 6.89303 8.50514 7.7028 7.82444C7.34898 7.04157 7.19949 6.18176 7.26832 5.32542C7.33714 4.46907 7.62203 3.64417 8.09634 2.92786C8.57064 2.21155 9.21887 1.62724 9.9804 1.22957C10.7419 0.831895 11.5919 0.63386 12.4507 0.653982C13.3096 0.674103 14.1494 0.911724 14.8914 1.34462C15.6335 1.77752 16.2536 2.39155 16.6939 3.12928C17.1341 3.86702 17.3801 4.70435 17.4087 5.56298C17.4374 6.42161 17.2478 7.27348 16.8577 8.03892C17.0417 8.20599 17.2189 8.38096 17.3905 8.56383C16.4588 8.61091 15.543 8.82343 14.6858 9.19146C14.2556 8.90775 13.7826 8.69507 13.2849 8.56157V9.97036C12.3716 10.6073 11.6024 11.4292 11.0272 12.3827V8.56157C8.45122 9.24113 6.51187 12.2021 6.51187 15.7523C6.51187 19.3025 8.45122 22.2634 11.0272 22.943V20.5273V20.5284ZM20.4892 9.02327C20.5987 8.74106 20.6619 8.43514 20.6788 8.09875C20.6941 7.79966 20.8275 7.51889 21.0498 7.3182C21.2721 7.11751 21.565 7.01334 21.8641 7.02861C22.1632 7.04388 22.4439 7.17733 22.6446 7.39961C22.8453 7.6219 22.9495 7.9148 22.9342 8.21389C22.9057 8.86307 22.754 9.50091 22.4872 10.0934C21.8762 9.6423 21.2032 9.28186 20.4892 9.02327ZM9.66133 6.6448C10.5056 6.30228 11.4114 6.13724 12.3222 6.15996C13.233 6.18268 14.1294 6.39268 14.9556 6.77688C15.1233 6.3543 15.1865 5.89744 15.1397 5.4452C15.0929 4.99295 14.9375 4.55871 14.6868 4.17943C14.4361 3.80015 14.0975 3.48706 13.6997 3.26681C13.302 3.04656 12.8569 2.92566 12.4024 2.91442C11.9479 2.90318 11.4973 3.00192 11.0892 3.20224C10.681 3.40256 10.3273 3.69853 10.0582 4.06495C9.78902 4.43137 9.61237 4.8574 9.54328 5.30678C9.47418 5.75616 9.51468 6.21557 9.66133 6.64593V6.6448ZM22.1632 20.1864L24.0371 22.5851C24.2162 22.8215 24.2951 23.1188 24.2567 23.4128C24.2184 23.7068 24.0659 23.974 23.8322 24.1565C23.5986 24.339 23.3025 24.4223 23.0079 24.3883C22.7133 24.3542 22.444 24.2057 22.258 23.9747L20.4846 21.7058C19.2805 22.4169 17.8552 22.6556 16.485 22.3757C15.1148 22.0958 13.8974 21.3172 13.0685 20.1908C12.2397 19.0644 11.8586 17.6705 11.999 16.2791C12.1394 14.8877 12.7913 13.5979 13.8285 12.6598C14.8656 11.7217 16.2141 11.202 17.6126 11.2015C19.011 11.201 20.3599 11.7197 21.3977 12.657C22.4356 13.5944 23.0885 14.8836 23.2299 16.2749C23.3713 17.6662 22.9912 19.0605 22.1632 20.1875V20.1864ZM17.2009 20.2067C17.6445 20.2657 18.0955 20.2361 18.5277 20.1197C18.9599 20.0033 19.3647 19.8024 19.7188 19.5287C20.0728 19.2549 20.3691 18.9136 20.5905 18.5246C20.8118 18.1356 20.9539 17.7065 21.0084 17.2623C21.0629 16.8181 21.0289 16.3674 20.9082 15.9364C20.7875 15.5054 20.5825 15.1026 20.3052 14.7513C20.0279 14.4 19.6836 14.1072 19.2924 13.8897C18.9012 13.6723 18.4708 13.5345 18.026 13.4844C17.1394 13.3846 16.2491 13.6387 15.5487 14.1916C14.8484 14.7445 14.3945 15.5514 14.2858 16.4371C14.1771 17.3227 14.4223 18.2155 14.9682 18.9213C15.514 19.6272 16.3163 20.0891 17.2009 20.2067Z" fill="url(#paint0_linear_1564_26015)"/>
+<defs>
+<linearGradient id="paint0_linear_1564_26015" x1="5.24901" y1="4.41879" x2="24.2154" y2="24.6088" gradientUnits="userSpaceOnUse">
+<stop stop-color="#A292FF"/>
+<stop offset="0.451923" stop-color="#FBCAFF"/>
+<stop offset="1" stop-color="#DB57FF"/>
+</linearGradient>
+</defs>
 </svg>
Index: src/components/MCP/MCPSubscribeButton/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Button, ButtonProps, message} from '@panda-design/components';\nimport {MouseEvent, useCallback, CSSProperties, useEffect, useMemo} from 'react';\nimport {Popconfirm} from 'antd';\nimport {useBoolean} from 'huse';\nimport {loadAppListForMCPServer, setMCPSubscribe, useAppListForMCPServer} from '@/regions/mcp/mcpServer';\nimport {IconSubscribeBlue} from '@/icons/mcp';\nimport {openMCPApplicationModal} from '@/regions/mcp/mcpApplication';\nimport {ApplicationBase} from '@/types/mcp/mcp';\nimport {IconSubscribeFilled} from '@/icons/mcp';\nimport {useMCPWorkspaceId} from '../hooks';\n\ninterface Props {\n    showText?: boolean;\n    showCount?: boolean;\n    id: number;\n    workspaceId: number;\n    enableGlobalParams?: boolean;\n    refresh?: () => void;\n    style?: CSSProperties;\n    size?: ButtonProps['size'];\n    iconColor?: string;\n    className?: string;\n}\n\nexport const MCPSubscribeButton = ({\n    id,\n    workspaceId,\n    style,\n    showText,\n    showCount,\n    size,\n    enableGlobalParams,\n    className,\n}: Props) => {\n    const [open, {on, off}] = useBoolean(false);\n    const spaceId = useMCPWorkspaceId();\n    const appList = useAppListForMCPServer(id);\n\n    const apps = useMemo<ApplicationBase[]>(\n        () => {\n            const data = appList?.reduce((acc, cur) => {\n                return [...acc, ...(spaceId && cur.workspaceId !== spaceId ? [] : cur.applications)];\n            }, []);\n            return data;\n        },\n        [appList, spaceId]\n    );\n    const subscribedNumber = useMemo(\n        () => (apps ?? []).filter(app => app.ifSub).length,\n        [apps]\n    );\n    const isSubscribed = subscribedNumber > 0;\n\n    const handleClick = useCallback(\n        async (e: MouseEvent) => {\n            e.stopPropagation();\n            try {\n                if (apps?.length) {\n                    setMCPSubscribe({\n                        serverId: id,\n                        workspaceId,\n                        enableGlobalParams: enableGlobalParams ?? false,\n                        onSuccess: () => loadAppListForMCPServer({mcpServerId: id}),\n                    });\n                }\n                else {\n                    on();\n                }\n            }\n            catch (e) {\n                message.error('服务器开小差了，请稍后重试');\n            }\n        },\n        [apps, id, workspaceId, enableGlobalParams, on]\n    );\n\n    const handleCancel = useCallback(\n        (e: MouseEvent) => {\n            e.stopPropagation();\n            off();\n        },\n        [off]\n    );\n\n    const handleOk = useCallback(\n        (e: MouseEvent) => {\n            e.stopPropagation();\n            off();\n            openMCPApplicationModal();\n        },\n        [off]\n    );\n\n\n    useEffect(\n        () => {\n            if (!spaceId && !apps?.length) {\n                loadAppListForMCPServer({mcpServerId: id});\n            }\n            if (spaceId) {\n                loadAppListForMCPServer({mcpServerId: id});\n            }\n        },\n        [apps?.length, id, spaceId]\n    );\n\n    return (\n        <Popconfirm\n            title=\"\"\n            description=\"您还没有可用应用，请先创建后订阅\"\n            open={open}\n            onConfirm={handleOk}\n            onCancel={handleCancel}\n            okText=\"立即创建\"\n            cancelText=\"稍后再说\"\n            placement=\"bottom\"\n        >\n            <Button\n                style={{padding: 0, gap: 3, ...style}}\n                onClick={handleClick}\n                className={className}\n                icon={\n                    isSubscribed\n                        ? <IconSubscribeFilled style={{color: '#FFA400'}} />\n                        : <IconSubscribeBlue />\n                }\n                type=\"text\"\n                size={size}\n            >\n                {showText\n                    ? '订阅' + (showCount ? ` ${subscribedNumber}` : '')\n                    : undefined\n                }\n            </Button>\n        </Popconfirm>\n    );\n};\n\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/MCPSubscribeButton/index.tsx b/src/components/MCP/MCPSubscribeButton/index.tsx
--- a/src/components/MCP/MCPSubscribeButton/index.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/components/MCP/MCPSubscribeButton/index.tsx	(date 1755162926634)
@@ -3,7 +3,7 @@
 import {Popconfirm} from 'antd';
 import {useBoolean} from 'huse';
 import {loadAppListForMCPServer, setMCPSubscribe, useAppListForMCPServer} from '@/regions/mcp/mcpServer';
-import {IconSubscribeBlue} from '@/icons/mcp';
+import {IconSubscribe} from '@/icons/mcp';
 import {openMCPApplicationModal} from '@/regions/mcp/mcpApplication';
 import {ApplicationBase} from '@/types/mcp/mcp';
 import {IconSubscribeFilled} from '@/icons/mcp';
@@ -18,7 +18,6 @@
     refresh?: () => void;
     style?: CSSProperties;
     size?: ButtonProps['size'];
-    iconColor?: string;
     className?: string;
 }
 
@@ -122,7 +121,7 @@
                 icon={
                     isSubscribed
                         ? <IconSubscribeFilled style={{color: '#FFA400'}} />
-                        : <IconSubscribeBlue />
+                        : <IconSubscribe />
                 }
                 type="text"
                 size={size}
Index: src/components/MCP/MCPServerCard/MCPServerTypeTag.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import styled from '@emotion/styled';\nimport {CSSProperties} from 'react';\nimport {getServerTypeIcon, getServerTypeText} from '@/components/MCP/MCPServerTypeTag';\nimport {MCPServerType} from '@/types/mcp/mcp';\n\nconst Wrapper = styled.span`\n    display: inline-flex;\n    align-items: center;\n    gap: 4px;\n    background: #DFE9FC;\n    border-top-right-radius: 6px;\n    border-bottom-left-radius: 6px;\n    color: #3678E9;\n    font-size: 12px;\n    line-height: 24px;\n    height: 24px;\n    padding: 0 8px 0 4px;\n    position: relative;\n\n    &::before {\n        content: '';\n        position: absolute;\n        left: -15px;\n        top: 0;\n        width: 0;\n        height: 0;\n        border-left: 18px solid transparent;\n        border-top: 24px solid #DFE9FC;\n        border-bottom-right-radius: 4px;\n    };\n`;\n\ninterface Props {\n    type: MCPServerType;\n    style?: CSSProperties;\n}\nexport default function MCPServerTypeTag({type, style}: Props) {\n\n    return (\n        <Wrapper style={style}>\n            {getServerTypeIcon(type)}\n            <span>{getServerTypeText(type)}</span>\n        </Wrapper>\n    );\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/MCPServerCard/MCPServerTypeTag.tsx b/src/components/MCP/MCPServerCard/MCPServerTypeTag.tsx
--- a/src/components/MCP/MCPServerCard/MCPServerTypeTag.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/components/MCP/MCPServerCard/MCPServerTypeTag.tsx	(date 1755169975832)
@@ -1,7 +1,7 @@
 import styled from '@emotion/styled';
 import {CSSProperties} from 'react';
 import {getServerTypeIcon, getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
-import {MCPServerType} from '@/types/mcp/mcp';
+import {MCPServerProtocolType} from '@/types/mcp/mcp';
 
 const Wrapper = styled.span`
     display: inline-flex;
@@ -31,7 +31,7 @@
 `;
 
 interface Props {
-    type: MCPServerType;
+    type: MCPServerProtocolType;
     style?: CSSProperties;
 }
 export default function MCPServerTypeTag({type, style}: Props) {
Index: src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {memo, MouseEvent, useCallback} from 'react';\nimport {useNavigate} from 'react-router-dom';\nimport {Button} from '@panda-design/components';\nimport cx from 'classnames';\nimport {css} from '@emotion/css';\nimport {MCPEditLink, MCPSpaceDetailLink} from '@/links/mcp';\nimport {MCPServerBase} from '@/types/mcp/mcp';\nimport {useMCPWorkspaceId} from '@/components/MCP/hooks';\nimport {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';\nimport BaseMCPCard from '@/components/MCP/BaseMCPCard';\nimport {actionButtonHoverStyle} from '@/components/MCP/BaseMCPCard/BaseMCPCard.styles';\nimport {MCPReleaseStatus} from '@/components/MCP/MCPReleaseStatus';\n\nconst cardContainerStyle = css`\n    position: relative;\n    &:hover {\n        z-index: 2;\n    }\n`;\n\ninterface Props {\n    server: MCPServerBase;\n    refresh: () => void;\n}\n\nconst SpaceMCPCard = ({server, refresh}: Props) => {\n    const spaceId = useMCPWorkspaceId();\n    const navigate = useNavigate();\n\n    const handleClick = useCallback(\n        () => {\n            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'tools'}));\n        },\n        [navigate, spaceId, server.id]\n    );\n\n    const handleViewCountClick = useCallback(\n        (e: MouseEvent) => {\n            e.stopPropagation();\n            navigate(MCPSpaceDetailLink.toUrl({mcpId: server.id, tab: 'overview'}));\n        },\n        [navigate, server.id]\n    );\n\n    const handleBasicInfoClick = useCallback(\n        (e: MouseEvent) => {\n            e.stopPropagation();\n            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'basicInfo'}));\n        },\n        [navigate, spaceId, server.id]\n    );\n\n    const handleToolsConfigClick = useCallback(\n        (e: MouseEvent) => {\n            e.stopPropagation();\n            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'tools'}));\n        },\n        [navigate, spaceId, server.id]\n    );\n\n    const renderActions = useCallback(\n        () => (\n            <>\n\n                <MCPSubscribeButton\n                    refresh={refresh}\n                    workspaceId={spaceId || server.workspaceId}\n                    id={server.id}\n                    className={cx(actionButtonHoverStyle)}\n                    showText={false}\n                    iconColor=\"#0083FF\"\n                />\n                <Button type=\"text\" onClick={handleBasicInfoClick} className={cx(actionButtonHoverStyle)}>\n                    基本信息\n                </Button>\n                <Button type=\"text\" onClick={handleToolsConfigClick} className={cx(actionButtonHoverStyle)}>\n                    工具配置\n                </Button>\n            </>\n        ),\n        [handleBasicInfoClick, handleToolsConfigClick, refresh, spaceId, server.workspaceId, server.id]\n    );\n\n    return (\n        <div className={cardContainerStyle}>\n            <BaseMCPCard\n                server={server}\n                refresh={refresh}\n                showDepartment={false}\n                workspaceId={spaceId}\n                onCardClick={handleClick}\n                onViewCountClick={handleViewCountClick}\n                renderActions={renderActions}\n                infoType=\"update\"\n            />\n            <MCPReleaseStatus\n                status={server.serverStatus}\n                publishType={server.serverPublishType}\n                style={{\n                    position: 'absolute',\n                    top: 1,\n                    right: 1,\n                    zIndex: 1,\n                }}\n            />\n        </div>\n    );\n};\n\nexport default memo(SpaceMCPCard);\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx b/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx
--- a/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx	(date 1755164895778)
@@ -68,7 +68,6 @@
                     id={server.id}
                     className={cx(actionButtonHoverStyle)}
                     showText={false}
-                    iconColor="#0083FF"
                 />
                 <Button type="text" onClick={handleBasicInfoClick} className={cx(actionButtonHoverStyle)}>
                     基本信息
@@ -91,7 +90,6 @@
                 onCardClick={handleClick}
                 onViewCountClick={handleViewCountClick}
                 renderActions={renderActions}
-                infoType="update"
             />
             <MCPReleaseStatus
                 status={server.serverStatus}
Index: src/components/MCP/BaseMCPCard/BaseMCPCard.styles.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import styled from '@emotion/styled';\nimport {css} from '@emotion/css';\nimport {colors} from '@/constants/colors';\n\nexport const containerCss = css`\n    padding: 16px 20px 12px;\n    position: relative;\n    transition: all 0.3s ease;\n    &:hover {\n        position: relative;\n        z-index: 1;\n        background: ${colors.white};\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n        border-radius: 6px;\n        padding-bottom: 48px;\n        margin-bottom: -48px;\n        width: auto;\n        .hover-actions {\n            opacity: 1;\n            min-height: 32px;\n            padding: 0 20px 16px;\n        }\n    }\n`;\n\nexport const hoverActionsStyle = css`\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    max-height: 0;\n    opacity: 0;\n    transition: all 0.3s ease;\n`;\n\nexport const DescriptionContainer = styled.div`\n    margin: 15px 0 13px;\n    padding: 10px 12px 9px;\n    background-color: ${colors['gray-3']};\n    font-size: 14px;\n    line-height: 1.4;\n    position: relative;\n    height: 57px;\n    overflow: hidden;\n`;\n\nexport const DescriptionText = styled.div`\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    text-overflow: ellipsis;\n    word-break: break-word;\n    overflow: hidden;\n`;\n\nexport const EllipsisOverlay = styled.div`\n    position: absolute;\n    bottom: 9px;\n    right: 12px;\n    padding-left: 10px;\n    background: linear-gradient(to right, transparent, ${colors['gray-3']} 50%);\n    pointer-events: none;\n`;\n\nexport const cardContentStyle = {\n    overflow: 'hidden',\n    flex: 1,\n};\n\nexport const protocolTextStyle = {\n    color: colors['gray-7'],\n    fontSize: 12,\n    lineHeight: '20px',\n};\n\nexport const departmentTextStyle = {\n    color: colors['gray-7'],\n    fontSize: 12,\n    marginBottom: 12,\n};\n\nexport const dividerStyle = {\n    margin: '16px 0 8px',\n};\n\nexport const statsContainerStyle = css`\n    cursor: pointer;\n    color: ${colors['gray-7']};\n    font-size: 12px;\n    line-height: 18px;\n    transition: color 0.2s ease;\n\n    &:hover {\n        color: ${colors.primary};\n    }\n`;\n\nexport const iconStyle = {\n    width: 14,\n    height: 14,\n};\n\nexport const formatCount = (count: number): string => {\n    if (count >= 10000) {\n        return `${Math.floor(count / 10000)}w+`;\n    }\n    if (count >= 1000) {\n        return `${Math.floor(count / 1000)}k+`;\n    }\n    return count.toString();\n};\n\nexport const actionButtonHoverStyle = css`\n    flex: 1;\n    background-color: #F2F2F2;\n    border-radius: 4px;\n    border: none;\n    padding: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    transition: all 0.2s ease;\n\n    &:hover {\n        background-color: #E6E6E6 !important;\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n        transform: translateY(-1px);\n    }\n`;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/BaseMCPCard/BaseMCPCard.styles.ts b/src/components/MCP/BaseMCPCard/BaseMCPCard.styles.ts
--- a/src/components/MCP/BaseMCPCard/BaseMCPCard.styles.ts	(revision 1660f49c901b9c6e6a7e0a41eab2d3181932a15d)
+++ b/src/components/MCP/BaseMCPCard/BaseMCPCard.styles.ts	(date 1755164912070)
@@ -34,13 +34,11 @@
 `;
 
 export const DescriptionContainer = styled.div`
-    margin: 15px 0 13px;
-    padding: 10px 12px 9px;
-    background-color: ${colors['gray-3']};
+    margin: 20px 0 12px;
     font-size: 14px;
-    line-height: 1.4;
+    line-height: 22px;
     position: relative;
-    height: 57px;
+    height: 44px;
     overflow: hidden;
 `;
 
@@ -73,23 +71,15 @@
     lineHeight: '20px',
 };
 
-export const departmentTextStyle = {
-    color: colors['gray-7'],
-    fontSize: 12,
-    marginBottom: 12,
-};
-
 export const dividerStyle = {
     margin: '16px 0 8px',
 };
 
 export const statsContainerStyle = css`
-    cursor: pointer;
     color: ${colors['gray-7']};
     font-size: 12px;
-    line-height: 18px;
+    line-height: 20px;
     transition: color 0.2s ease;
-
     &:hover {
         color: ${colors.primary};
     }
@@ -100,6 +90,18 @@
     height: 14,
 };
 
+export const actionButtonStyle = {
+    fontSize: '12px',
+    lineHeight: '20px',
+    padding: 0,
+    height: 20,
+    color: '#545454',
+};
+
+export const fullWidthButtonStyle = {
+    width: '100%',
+};
+
 export const formatCount = (count: number): string => {
     if (count >= 10000) {
         return `${Math.floor(count / 10000)}w+`;
