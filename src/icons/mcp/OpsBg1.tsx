import type { SVGProps } from "react";
const SvgOpsBg1 = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 70 70"
        {...props}
    >
        <g filter="url(#opsBg1_svg__a)" opacity={0.2}>
            <circle cx={35} cy={35} r={19} fill="#C1B8FF" fillOpacity={0.8} />
        </g>
        <defs>
            <filter
                id="opsBg1_svg__a"
                width={69.6}
                height={69.6}
                x={0.2}
                y={0.2}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feBlend
                    in="SourceGraphic"
                    in2="BackgroundImageFix"
                    result="shape"
                />
                <feGaussianBlur
                    result="effect1_foregroundBlur_1643_12893"
                    stdDeviation={7.9}
                />
            </filter>
        </defs>
    </svg>
);
export default SvgOpsBg1;
