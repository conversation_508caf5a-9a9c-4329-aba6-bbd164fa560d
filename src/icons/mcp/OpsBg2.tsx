import type { SVGProps } from "react";
const SvgOpsBg2 = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 84 80"
        {...props}
    >
        <path
            fill="url(#opsBg2_svg__a)"
            fillOpacity={0.8}
            d="M16.815 10.812c3.869-2.388 9.027-1.102 11.238 2.938 2.394 3.856 1.104 8.999-2.948 11.202-2.027 1.102-4.422 1.285-6.448.734-4.42 8.448-4.42 19.1.738 27.914 6.447 11.018 18.973 16.343 30.947 14.14 1.473-.184 2.763.55 3.132 2.203.368 1.653-.553 3.122-2.211 3.49-14.184 2.57-29.105-3.674-36.657-16.896-6.264-10.834-5.896-23.689-.37-33.972-.184-.184-.184-.368-.368-.551-2.395-3.857-1.105-8.999 2.947-11.202m21.921-6.244c14-2.57 28.922 3.673 36.659 16.895 6.262 10.834 5.894 23.689.368 33.973.184.183.184.367.368.367 2.395 3.856 1.105 8.998-2.947 11.202-3.869 2.387-9.026 1.101-11.237-2.939-2.395-3.856-1.105-8.997 2.947-11.2 2.027-1.102 4.421-1.287 6.448-.736 4.42-8.447 4.42-19.098-.737-27.912C64.156 13.2 51.63 7.874 39.657 10.078c-1.473.183-2.762-.735-3.13-2.02-.369-1.653.551-3.122 2.21-3.49m32.606 54.173c-.737-1.285-2.395-1.652-3.685-.918-1.29.735-1.657 2.388-.92 3.673.736 1.285 2.395 1.653 3.684.918s1.657-2.388.92-3.673M44.078 18.296a1.58 1.58 0 0 1 1.843 0l8.289 5.134c.553.395.92.986.92 1.776v6.91c0 .79.37 1.383.922 1.778l5.526 3.356c.553.395.921.988.921 1.777v10.267c0 .79-.368 1.382-.92 1.776l-8.29 5.134a1.58 1.58 0 0 1-1.842 0L45 52.254l-6.447 3.95a1.58 1.58 0 0 1-1.842 0l-8.29-5.134c-.552-.395-.92-.986-.92-1.776V39.027c0-.79.368-1.382.92-1.777l5.527-3.356c.553-.395.921-.988.921-1.778v-6.91c0-.79.369-1.381.921-1.776zm-11.052 23.1v5.726l4.605 2.764 4.605-2.764v-5.725l-4.605-2.765zm14.737 0v5.726l4.605 2.764 4.605-2.764v-5.725l-4.605-2.765zm-7.368-13.82v5.726l4.604 2.763 4.605-2.763v-5.727L45 24.811zM23.263 16.32c-.737-1.285-2.395-1.653-3.684-.919-1.29.735-1.658 2.388-.922 3.673.737 1.286 2.395 1.654 3.685.92 1.29-.735 1.657-2.388.92-3.674"
        />
        <defs>
            <linearGradient
                id="opsBg2_svg__a"
                x1={35.5}
                x2={36}
                y1={-11}
                y2={74.001}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#77A2FF" stopOpacity={0.4} />
                <stop offset={1} stopColor="#D9E8FF" stopOpacity={0} />
            </linearGradient>
        </defs>
    </svg>
);
export default SvgOpsBg2;
