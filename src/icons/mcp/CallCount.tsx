import type { SVGProps } from "react";
const SvgCallCount = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 12 12"
        {...props}
    >
        <path
            fill="#545454"
            d="M8.719.938c.466 0 .843.377.844.843v3.89h-.938V2.199a.33.33 0 0 0-.328-.323H2.573a.33.33 0 0 0-.323.328v7.318a.33.33 0 0 0 .328.323h3.235v.937H2.156a.844.844 0 0 1-.844-.844V1.781c0-.466.378-.843.844-.843z"
        />
        <path
            fill="#545454"
            d="M10.88 8.516 8.619 10.78l-.672-.672 1.116-1.116H5.587v-.95h3.475L7.946 6.925l.672-.671zM7.031 5.79H3.75v-.938h3.281zM7.031 3.797H3.75v-.938h3.281z"
        />
    </svg>
);
export default SvgCallCount;
