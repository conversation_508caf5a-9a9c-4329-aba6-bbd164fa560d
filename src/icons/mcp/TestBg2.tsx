import type { SVGProps } from "react";
const SvgTestBg2 = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 94 80"
        {...props}
    >
        <path
            fill="url(#testBg2_svg__a)"
            fillOpacity={0.8}
            d="M52.66 60.34a22.5 22.5 0 0 0 6.367 6.838v.006h.006a22.1 22.1 0 0 0 7.61 3.415c-3.2 2.166-6.88 3.401-10.8 3.401-7.839 0-14.732-4.937-18.705-12.406-2.76.026-4.388.618-5.12 1.546-1.016 1.29-1.56 2.777-1.65 4.56a3.22 3.22 0 0 1-1.046 2.213 3.18 3.18 0 0 1-2.298.822 3.17 3.17 0 0 1-2.203-1.052 3.2 3.2 0 0 1-.817-2.31c.16-3.094 1.169-5.859 3.018-8.207 1.72-2.183 4.315-3.414 7.651-3.82A32 32 0 0 1 33.705 50h-6.517a3.17 3.17 0 0 1-2.252-.937 3.2 3.2 0 0 1-.932-2.262 3.2 3.2 0 0 1 .932-2.263 3.18 3.18 0 0 1 2.252-.937h6.52a32 32 0 0 1 1.242-6.297c-2.846-.519-5.084-1.712-6.622-3.664-1.853-2.349-2.866-5.113-3.022-8.208a3.2 3.2 0 0 1 .817-2.308 3.175 3.175 0 0 1 4.498-.23 3.2 3.2 0 0 1 1.047 2.212c.089 1.782.637 3.27 1.65 4.56.652.828 2.02 1.388 4.275 1.516a25.4 25.4 0 0 1 5.69-6.854 14.46 14.46 0 0 1 1.11-13.88 14.35 14.35 0 0 1 5.314-4.813 14.27 14.27 0 0 1 13.851.326 14.36 14.36 0 0 1 5.084 5.058 14.45 14.45 0 0 1 .462 13.917q.778.711 1.503 1.488a22.1 22.1 0 0 0-7.629 1.78 13.5 13.5 0 0 0-3.95-1.786v3.993a22.5 22.5 0 0 0-6.369 6.838V26.42c-7.265 1.926-12.735 10.32-12.735 20.383s5.47 18.456 12.735 20.382v-6.847zm26.687-32.613c.309-.8.487-1.667.535-2.62a3.2 3.2 0 0 1 1.046-2.213 3.175 3.175 0 0 1 4.498.231c.566.63.86 1.46.817 2.308a14.6 14.6 0 0 1-1.26 5.328 22.2 22.2 0 0 0-5.636-3.033m-30.54-6.742a18.64 18.64 0 0 1 14.932.375 8.04 8.04 0 0 0-.758-7.363 8 8 0 0 0-2.784-2.587 7.93 7.93 0 0 0-10.27 2.263 8.036 8.036 0 0 0-1.12 7.316zm35.262 38.386 5.285 6.8a3.21 3.21 0 0 1-.578 4.453 3.173 3.173 0 0 1-4.44-.515l-5.002-6.432a15.85 15.85 0 0 1-11.281 1.9c-3.865-.794-7.298-3-9.636-6.194a16.06 16.06 0 0 1 2.143-21.347 15.88 15.88 0 0 1 10.673-4.134 15.88 15.88 0 0 1 10.676 4.126 16.058 16.058 0 0 1 2.16 21.346zm-13.997.057a9.5 9.5 0 0 0 7.102-1.922 9.6 9.6 0 0 0 2.458-2.846 9.63 9.63 0 0 0 .897-7.336 9.6 9.6 0 0 0-1.701-3.36 9.6 9.6 0 0 0-2.857-2.442 9.52 9.52 0 0 0-10.559.856 9.634 9.634 0 0 0-1.638 13.407c1.54 2 3.803 3.31 6.298 3.643"
        />
        <defs>
            <linearGradient
                id="testBg2_svg__a"
                x1={57}
                x2={57.472}
                y1={-27}
                y2={67.998}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#A17FFF" stopOpacity={0.4} />
                <stop offset={1} stopColor="#C977FF" stopOpacity={0} />
            </linearGradient>
        </defs>
    </svg>
);
export default SvgTestBg2;
