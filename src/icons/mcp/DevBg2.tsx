import type { SVGProps } from "react";
const SvgDevBg2 = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 84 80"
        {...props}
    >
        <path
            fill="url(#devBg2_svg__a)"
            fillOpacity={0.8}
            d="M76.5 4A3.5 3.5 0 0 1 80 7.5v63a3.5 3.5 0 0 1-3.5 3.5h-70A3.5 3.5 0 0 1 3 70.5v-63A3.5 3.5 0 0 1 6.5 4zM73 11H10v56h63zm-30.336 8.767 3.106.347a1.75 1.75 0 0 1 1.54 1.976l-4.498 32.888a1.75 1.75 0 0 1-1.924 1.501l-3.107-.345a1.75 1.75 0 0 1-1.54-1.976L40.74 21.27a1.75 1.75 0 0 1 1.925-1.501M29.406 28.43l2.194 2.037a1.75 1.75 0 0 1 .061 2.506l-6.037 6.174 6.038 6.174a1.75 1.75 0 0 1-.028 2.474l-.034.032-2.194 2.037a1.75 1.75 0 0 1-2.433-.049L17.6 40.381a1.75 1.75 0 0 1 0-2.468l9.373-9.434a1.75 1.75 0 0 1 2.433-.049m26.62.049 9.374 9.434a1.75 1.75 0 0 1 0 2.468l-9.373 9.432a1.75 1.75 0 0 1-2.433.053L51.4 47.827a1.75 1.75 0 0 1-.061-2.506l6.037-6.174-6.037-6.174a1.75 1.75 0 0 1 .06-2.506l2.195-2.037a1.75 1.75 0 0 1 2.433.049"
        />
        <defs>
            <linearGradient
                id="devBg2_svg__a"
                x1={42}
                x2={35.108}
                y1={-38}
                y2={79.655}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#1193FF" stopOpacity={0.3} />
                <stop offset={1} stopColor="#CDE8FF" stopOpacity={0.05} />
            </linearGradient>
        </defs>
    </svg>
);
export default SvgDevBg2;
