/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Flex, Typography} from 'antd';
import {ReactNode} from 'react';
import {IconArrowRight, IconDev, IconOps, IconTest} from '@/icons/mcp';
import {MCPZoneLink} from '@/links/mcp';

const NavigationContainer = styled(Flex)`
    margin-top: 20px;
    gap: 12px;
`;

const RegionCard = styled.div<{ isActive?: boolean, disabled?: boolean, regionType?: string }>`
    flex: 1;
    padding: 24px 20px;
    border: 1px solid #D9D9D9;
    border-radius: 10px;
    background: ${props => {
        switch (props.regionType) {
            case 'dev':
                return 'linear-gradient(329.12deg, #E7F1FF -8.74%, #FFFFFF 92.24%)';
            case 'ops':
                return 'linear-gradient(328.39deg, #DEF3FF -8.86%, #FFFFFF 89.14%)';
            case 'test':
                return 'linear-gradient(328.02deg, #F0ECFE -8.92%, #FFFFFF 92.24%)';
            default:
                return 'linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%)';
        }
    }};
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    &:hover {
        ${props =>
        !props.disabled
            && `
            border-color: #0083FF;
            transform: translateY(-2px);
            box-shadow: 0px 5px 16px 0px #00000021;
        `}
    }
`;

const CardHeader = styled(Flex)`
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
`;

const IconWrapper = styled.div`
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    svg {
        font-size: 20px;
    }
`;

const CardTitle = styled(Typography.Title)`
    margin: 0 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    line-height: 28px !important;
`;

const CardDescription = styled(Typography.Text)`
    font-size: 12px;
    line-height: 20px;
    color: #5C5C5C;
`;

const HoverActionContainer = styled(Flex)`
    align-items: center;
    gap: 8px;
`;

const HoverText = styled.span`
    color: #0083FF;
    font-size: 12px;
    line-height: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
    white-space: nowrap;
`;

const ArrowIcon = styled(IconArrowRight)`
    color: #8c8c8c;
    transition: all 0.3s ease;
    font-size: 16px;

    svg {
        transition: all 0.3s ease;
    }

    path {
        fill: #8c8c8c !important;
        transition: fill 0.3s ease;
    }
`;

const StyledRegionCard = styled(RegionCard)`
    cursor: pointer;
    &:hover {
        ${HoverText} {
            opacity: 1;
        }

        ${ArrowIcon} {
            color: #0083FF;
            path {
                fill: #0083FF !important;
            }
        }
    }
`;

interface RegionItem {
    key: string;
    title: string;
    description: string;
    icon: ReactNode;
    status?: 'active' | 'coming';
    disabled?: boolean;
    onClick?: () => void;
    // 用id来跳转
    id: string;
}

interface Props {
    activeRegion?: string;
    onRegionChange?: (region: string) => void;
}

const RegionNavigation = ({activeRegion = 'dev', onRegionChange}: Props) => {
    const regions: RegionItem[] = [
        {
            key: 'dev',
            title: '开发专区',
            description:
                '面向研发场景提供丰富且高质量的MCP工具，覆盖DevOps 研发流程及AI应用开发的各环节，助力研发提效。 ',
            icon: <IconDev />,
            status: 'active',
            disabled: false,
            id: '5',
        },
        {
            key: 'ops',
            title: '运维专区',
            description:
                '面向运维的MCP中心，汇聚各类高质量工具，在通用运维、云运维、业务运维等场景助力快速定位故障、分析根因、提升排障效率。',
            icon: <IconOps />,
            status: 'active',
            disabled: false,
            id: '3',
        },
        {
            key: 'test',
            title: '测试专区',
            description:
                '由各QA团队共建共享常用的测试工具、测试平台MCP Server，加速测试流程智能化改造。',
            icon: <IconTest />,
            status: 'active',
            disabled: false,
            id: '1',
        },
    ];

    const handleRegionClick = (region: RegionItem) => {
        if (!region.disabled && region.status === 'active') {
            onRegionChange?.(region.key);
            window.open(MCPZoneLink.toUrl({zoneId: region.id}), '_blank');
        }
    };

    return (
        <NavigationContainer>
            {regions.map(region => (
                <StyledRegionCard
                    key={region.key}
                    isActive={activeRegion === region.key}
                    disabled={region.disabled}
                    regionType={region.key}
                    onClick={() => handleRegionClick(region)}
                >
                    <CardHeader>
                        <Flex>
                            <IconWrapper>{region.icon}</IconWrapper>
                            <CardTitle level={4}>{region.title}{region.status === 'coming' ? '（敬请期待）' : ''}</CardTitle>
                        </Flex>
                        <HoverActionContainer>
                            <HoverText>进入专区</HoverText>
                            <ArrowIcon />
                        </HoverActionContainer>
                    </CardHeader>
                    <CardDescription>{region.description}</CardDescription>
                </StyledRegionCard>
            ))}
        </NavigationContainer>
    );
};

export default RegionNavigation;
